{"common": {"hello": "Hello", "save": "Save", "cancel": "Cancel", "loading": "Loading...", "retry": "Retry", "tryAgain": "Try Again", "back": "Back", "done": "Done", "ok": "OK", "yes": "Yes", "no": "No", "delete": "Delete", "edit": "Edit", "view": "View", "add": "Add", "create": "Create", "update": "Update", "remove": "Remove", "search": "Search...", "searchPlaceholder": "Search...", "noResults": "No results found", "error": "Error", "success": "Success", "price": "Price (Optional)", "description": "Description (Optional)", "unit": "Unit", "selectUnit": "Select unit", "selectFrequency": "Select frequency", "selectDuration": "Select duration", "patient": "Patient", "or": "OR", "unknownError": "Unknown error", "retryNotImplemented": "Retry functionality needs to be implemented."}, "settings": {"title": "Settings", "language": "Language", "logout": "Log Out", "medicineNotifications": "Medicine Notifications", "medicineExpirationNotifications": "💊 Medicine Expiration Notifications", "sendTestNotification": "🧪 Send Test Notification", "howItWorks": "ℹ️ How it works", "loading": "Loading...", "languageChangeSuccess": "Language changed to {{language}}", "languageChangeError": "Failed to change language. Please try again.", "languageNote": "The app language will change immediately after selection.", "loadingNotificationSettings": "Loading notification settings...", "enableNotifications": "Enable Notifications", "getNotifiedWhenExpiring": "Get notified when your medications are about to expire", "notificationTime": "Notification Time", "timeWhenDailyNotificationsSent": "Time when daily notifications will be sent", "whenToNotify": "When to Notify", "threeDaysBefore": "3 Days Before", "earlyWarningForRefills": "Early warning for refills", "oneDayBefore": "1 Day Before", "tomorrowExpirationReminder": "Tomorrow expiration reminder", "dayOfExpiration": "Day of Expiration", "expiresTodayAlert": "Expires today alert", "status": "📋 Status", "notificationsScheduled": "{{count}} notification{{count, plural, one {} other {s}}} scheduled", "noNotificationsScheduled": "No notifications currently scheduled", "enableNotificationsToReceiveAlerts": "Enable notifications to receive alerts when your medications are about to expire", "selectTime": "Select Time", "howItWorksDescription": "• Notifications are automatically scheduled based on your medication expiration dates\n• You will receive alerts even when the app is closed\n• Notifications are updated whenever you add, edit, or remove medications\n• Make sure to allow notifications in your device settings", "deleteAccount": "Delete Account", "deleteAccountConfirmation": "Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently lost.", "processing": "Processing...", "profileNotFound": "Profile not found", "errorFetchingEmail": "Error fetching email", "user": "User", "noEmailFound": "No email found", "account": "Account", "profile": "Profile", "security": "Security", "session": "Session", "about": "About", "aboutApp": "About App", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "version": "Version 1.0.0", "showOnboarding": "Show Onboarding", "showOnboardingDescription": "Show the app introduction screens again", "resetOnboardingTitle": "Reset Onboarding", "resetOnboardingMessage": "This will show the onboarding screens again the next time you open the app.", "resetOnboardingSuccess": "Onboarding Reset", "resetOnboardingSuccessMessage": "Onboarding will be shown next time you restart the app."}, "auth": {"signIn": "Sign In", "signUp": "Sign Up", "email": "Email", "password": "Password", "forgotPassword": "Forgot Password", "resetPassword": "Reset Password", "verifyEmail": "<PERSON><PERSON><PERSON>", "welcomeBack": "Welcome Back", "emailPlaceholder": "<EMAIL>", "passwordPlaceholder": "••••••••", "signInWithGoogle": "Sign in with Google", "signInWithApple": "Sign in with Apple", "needAccount": "Need an account?", "loginIncomplete": "Login Incomplete", "furtherStepsRequired": "Further steps may be required to complete your sign in.", "loginFailed": "Login Failed", "socialSignInError": "Could not sign in with the selected provider."}, "navigation": {"prescriptions": "Prescriptions", "medicines": "Medicines", "allMedicines": "All Medicines", "doctors": "Doctors", "allDoctors": "All Doctors", "patients": "Patients", "labResults": "Lab Results", "notifications": "Notifications", "tracker": "Tracker", "settings": "Settings", "aiChat": "AI Chat"}, "prescriptions": {"title": "Prescriptions", "noPrescriptions": "No Prescriptions Yet", "noPrescriptionsMessage": "Add your first prescription, including images if you have them!", "addFirstPrescription": "Add First Prescription", "failedToLoad": "Failed to load prescriptions", "loadingPrescriptions": "Loading prescriptions...", "addedSuccessfully": "Prescription added successfully.", "updatedSuccessfully": "Prescription updated successfully.", "saveFailed": "Failed to {{action}} prescription.", "notFound": "Prescription not found.", "loadingFailed": "Prescription not found or failed to load."}, "medicines": {"title": "Medicines", "allMedicines": "All Medicines", "myMedicines": "My Medicines", "noMedicines": "No Medicines Yet", "noMedicinesMessage": "Add your first medicine to get started!", "addFirstMedicine": "Add First Medicine", "addToMyMedications": "Add to My Medications", "failedToLoad": "Failed to load medicines", "loadingMedicines": "Loading medicines...", "dosageUnit": "Dosage Unit", "frequency": "Frequency", "duration": "Duration", "selectDosageUnit": "Select Dosage Unit", "selectFrequencyUnit": "Select Frequency Unit", "selectDurationUnit": "Select Duration Unit", "selectPatient": "Select Patient", "receiptImage": "Receipt Image", "reviewExtractedMedicines": "Review Extracted Medicines", "scanReceipt": "Scan Receipt", "chooseCapture": "Choose how to capture your pharmacy receipt:", "camera": "Camera", "photoLibrary": "Photo Library", "expiresOn": "Expires on", "errorLoadingMedicines": "Error loading medicines", "addedSuccessfully": "Medicine added successfully.", "updatedSuccessfully": "Medicine updated successfully.", "saveFailed": "Failed to save medicine. Please try again.", "ocrProcessingFailed": "Failed to process receipt. Please try again.", "ocrAnalyzingReceipt": "Analyzing receipt and extracting medicines...", "ocrNoMedicinesFound": "No Medicines Found", "ocrNoMedicinesFoundMessage": "No medicines found in this receipt. Please make sure the image is clear and contains medicine information.", "ocrTryAgain": "Try Again", "ocrProcessingFailedMessage": "Failed to process receipt: {{error}}"}, "doctors": {"title": "Doctors", "allDoctors": "All Doctors", "myDoctors": "My Doctors", "noDoctors": "No Doctors Yet", "noDoctorsMessage": "Add your first doctor to get started!", "addFirstDoctor": "Add First Doctor", "addToMyDoctors": "Add to My Doctors", "failedToLoad": "Failed to load doctors", "loadingDoctors": "Loading doctors...", "selectDoctor": "Select Doctor", "deleteError": "Failed to delete doctor", "addedSuccessfully": "Doctor added successfully.", "updatedSuccessfully": "Doctor updated successfully.", "saveFailed": "Failed to {{action}} doctor."}, "patients": {"title": "Patients", "noPatients": "No Patients Yet", "noPatientsMessage": "Add your first patient to get started!", "addFirstPatient": "Add First Patient", "failedToLoad": "Failed to load patients", "loadingPatients": "Loading patients...", "selectPatient": "Select Patient", "selectBloodType": "Select Blood Type", "deleteError": "Failed to delete patient", "createdSuccessfully": "Patient created successfully.", "updatedSuccessfully": "Patient updated successfully.", "saveFailed": "Failed to {{action}} patient."}, "labResults": {"title": "Lab Results", "noLabResults": "No Lab Results Yet", "noLabResultsMessage": "Add your first lab result to get started!", "addFirstLabResult": "Add First Lab Result", "failedToLoad": "Failed to load lab results", "loadingLabResults": "Loading lab results...", "viewLabResult": "View Lab Result", "checkResults": "Check Results", "viewSavedPdf": "View Saved PDF", "copyPatientId": "Copy Patient ID", "copyPassword": "Copy Password", "createdSuccessfully": "Lab result created successfully", "updatedSuccessfully": "Lab result updated successfully", "updatedWithPdf": "Lab result updated with additional PDF successfully!", "replacedWithPdf": "Lab result updated by replacing PDF successfully!", "saveFailed": "Failed to {{action}} lab result. Please try again.", "updateFailed": "Failed to update lab result ({{action}}).", "updatingWithPdf": "Updating lab result with PDF...", "notFound": "Lab result not found.", "loadingFailed": "Failed to load lab result details.", "webviewTitle": "Lab Results Portal", "webviewCopyPatientId": "Copy Patient ID", "webviewCopyPassword": "Copy Password", "webviewPatientId": "Patient ID", "webviewPassword": "Password", "webviewCannotSavePdf": "Cannot save PDF at this time. Missing WebView reference or Lab Result ID.", "webviewPdfCapturedSuccess": "PDF captured successfully!", "webviewLabResultIdNotFound": "Lab Result ID not found, cannot attach PDF.", "webviewUrlMissing": "Website URL is missing.", "webviewLoadFailed": "Failed to load the page.", "webviewLoadingWebsite": "Loading website...", "webviewCapturingPdf": "Capturing PDF...", "webviewCopiedToClipboard": "{{type}} copied to clipboard!", "webviewCopyFailed": "Failed to copy {{type}}.", "webviewPdfCaptureFailed": "Failed to initiate PDF capture. {{error}}", "webviewPdfGenerationFailed": "Failed to generate PDF. {{error}}"}, "aiChat": {"title": "AI Health Assistant", "newChat": "New Chat", "generalChat": "General <PERSON><PERSON>", "labAnalysis": "Lab Analysis", "medicationAnalysis": "Medication Analysis", "startConversation": "Start Conversation", "welcomeMessage": "Ask me questions about your health, medications, lab results, or get general medical guidance.", "disclaimer": "Always consult with healthcare professionals for medical advice and treatment decisions.", "labResultAnalysis": "Lab Result Analysis", "labResultAnalysisSubtitle": "I've analyzed your lab results and I'm ready to answer your questions.", "labResultAnalysisDescription": "Ask me about specific values, what they mean, trends over time, or any concerns you might have.", "medicationAnalysisTitle": "Medication Analysis", "medicationAnalysisSubtitle": "I've analyzed your medication and I'm ready to help.", "medicationAnalysisDescription": "Ask me about interactions, side effects, dosing, or any other medication-related questions.", "generalChatTitle": "AI Health Assistant", "generalChatSubtitle": "I'm here to help with your health questions.", "generalChatDescription": "Ask me about symptoms, medications, lab results, or general health guidance. Remember to always consult healthcare professionals for medical decisions.", "medicalDisclaimer": "This AI assistant provides educational information only. Always consult with healthcare professionals for medical advice.", "continueChat": "<PERSON><PERSON><PERSON>", "deleteChat": "Delete Chat", "deleteSession": "Delete Session", "untitledChat": "Untitled Chat", "noChatsYet": "No chats yet", "noChatsMatch": "No chats match your search", "searchChats": "Search chats...", "deleting": "Deleting...", "failedToOpenChat": "Failed to open chat. Please try again.", "failedToCreateChat": "Failed to create new chat", "failedToDeleteChat": "Failed to delete chat", "failedToSendMessage": "Failed to send message. Please try again.", "failedToDeleteSession": "Failed to delete session. Please try again."}, "notifications": {"title": "Notifications", "medicineExpiration": "Medicine Expiration", "testNotification": "Test Notification", "noExpiringMedicinesOnDate": "No expiring medicines on {{date}}.", "noExpiringMedicinesFound": "No expiring medicines found."}, "modals": {"selectOption": "Select Option", "createNew": "Create New", "selectPatientO": "Select Patient O", "noResults": "No results found"}, "actions": {"manual": "Manual", "fromCamera": "From Camera", "fromGallery": "From Gallery", "fromBill": "From Bill", "plus": "Plus", "receipt": "Receipt", "folder": "Folder"}, "forms": {"required": "This field is required", "invalidEmail": "Please enter a valid email address", "passwordTooShort": "Password must be at least 8 characters", "selectRequired": "Please make a selection"}, "ocr": {"scanning": "Scanning receipt...", "processing": "Processing image...", "extracting": "Extracting medicines...", "reviewMedicines": "Review extracted medicines", "noMedicinesFound": "No medicines found in the receipt", "tryAgain": "Try taking another photo or select a different image", "scanningError": "Error scanning receipt", "uploadError": "Error uploading image", "extractedFromReceipt": "This medicine was extracted from a pharmacy receipt"}, "alerts": {"confirmDelete": "Are you sure you want to delete this item?", "deleteSuccess": "Item deleted successfully", "saveSuccess": "<PERSON><PERSON> saved successfully", "updateSuccess": "Item updated successfully", "createSuccess": "Item created successfully", "deleteDoctorMessage": "Are you sure you want to delete this doctor? Related prescriptions might be affected.", "deletePatientMessage": "Are you sure you want to delete this patient? This action cannot be undone.", "failedToOpenPdf": "Failed to open PDF. Please try again.", "pdfNotFound": "PDF not found or still loading. Please try again.", "documentError": "Document Error", "imageCaptureError": "Image Capture Error", "permissionRequired": "Permission Required", "testNotificationSent": "Test Sent", "testNotificationMessage": "A test notification has been sent. You should see it now!", "failedToSendTest": "Failed to send test notification", "failedToLoadSettings": "Failed to load notification settings", "failedToSaveSettings": "Failed to save notification settings", "pleaseEnableNotifications": "Please enable notifications to test the feature.", "twoFactorRequired": "Two-Factor Authentication Required", "twoFactorMessage": "Please complete the second factor verification.", "signInFailed": "Sign In Failed", "signInFailedMessage": "Could not complete sign in.", "invalidCredentials": "Invalid email or password.", "loginIncomplete": "Login Incomplete", "loginIncompleteMessage": "Further steps may be required to complete your sign in.", "loginFailed": "Login Failed", "verificationFailed": "Verification Failed", "verificationFailedMessage": "Sign up process requires further steps.", "invalidCode": "Invalid code or verification expired.", "codeResent": "Code Resent", "codeResentMessage": "A new verification code has been sent to your email.", "errorResendingCode": "Error Resending Code", "couldNotResendCode": "Could not resend verification code.", "couldNotSendReset": "Could not send password reset email.", "enterValidCode": "Please enter a valid 6-digit code.", "couldNotVerifyCode": "Could not verify code.", "passwordResetSuccess": "Your password has been reset successfully.", "couldNotResetPassword": "Could not reset password.", "signUpFailed": "Sign Up Failed", "unexpectedError": "An unexpected error occurred.", "failedToSignOut": "Failed to sign out. Please try again.", "failedToDeleteAccount": "Failed to delete account. Please try again."}, "accessibility": {"button": "<PERSON><PERSON>", "image": "Image", "textInput": "Text input", "searchField": "Search field", "menuButton": "Menu button", "backButton": "Back button", "closeButton": "Close button"}, "placeholders": {"enterName": "Enter name", "enterEmail": "Enter email address", "enterPhone": "Enter phone number", "enterAddress": "Enter address", "enterNotes": "Enter notes", "selectDate": "Select date", "selectDoctor": "Select doctor", "selectPatient": "Select patient", "selectMedicine": "Select medicine", "search": "Search...", "typeMessage": "Type your message...", "medicineName": "e.g., Paracetamol 500mg", "dosage": "e.g., 500", "form": "e.g., Tablet, Capsule", "manufacturer": "Enter manufacturer", "doctorName": "Enter doctor's full name", "specialty": "e.g., Cardiologist, General Practitioner", "licenseNumber": "Enter license number", "patientName": "Enter patient's full name", "age": "Enter patient's age", "testName": "Enter test name", "laboratory": "Enter laboratory name", "results": "Enter test results", "referenceValues": "Normal range values", "price": "e.g., 10.99", "description": "e.g., For headache relief", "frequency": "e.g., 3", "duration": "e.g., 7", "workplace": "e.g., Central Hospital, Private Clinic", "enterCity": "Enter city", "enterFee": "Enter consultation fee", "youAtExample": "<EMAIL>", "passwordPlaceholder": "••••••••", "sixDigitCode": "123456", "tapToAddLabImage": "Tap to add lab result image", "enterLabName": "Enter lab name", "enterPatientsNameAsAppears": "Enter patient's name as it appears on the lab result", "enterPatientIdFromLab": "Enter patient ID from lab result", "enterLabPassword": "Enter lab password", "searchPatients": "Search patients...", "selectResultDate": "Select result date", "enterLabWebsite": "Enter lab website", "enterAlternatePhone": "Enter alternate phone number", "enterAnotherPhone": "Enter another phone number", "monthlyCheckupExample": "e.g., Monthly Checkup Rx", "selectDoctorOptional": "Select doctor (optional)", "searchDoctors": "Search doctors...", "anyAdditionalDetails": "Any additional details...", "selectPatientOptional": "Select a patient (optional)", "createNewPatient": "Create New Patient", "enterAnyNotes": "Enter any notes", "selectExpirationDate": "Select expiration date", "selectDateOpened": "Select date opened", "takeMedWithFood": "e.g., Take with food"}, "buttons": {"addMedicine": "Add Medicine", "editMedicine": "Edit Medicine", "deleteMedicine": "Delete Medicine", "addDoctor": "Add Doctor", "editDoctor": "Edit Doctor", "deleteDoctor": "Delete Doctor", "addPatient": "Add Patient", "editPatient": "<PERSON>", "deletePatient": "Delete Patient", "addPrescription": "Add Prescription", "editPrescription": "Edit Prescription", "deletePrescription": "Delete Prescription", "addLabResult": "Add Lab Result", "editLabResult": "Edit Lab Result", "deleteLabResult": "Delete Lab Result", "analyzeWithAI": "Analyze with AI", "viewAnalysis": "View Analysis", "retryAnalysis": "Try Again", "checkPrices": "Check Prices", "scanReceipt": "Scan Receipt", "attachPDF": "Attach PDF", "viewPDF": "View PDF", "shareResult": "Share Result", "copyToClipboard": "Copy to Clipboard", "callDoctor": "Call Doctor", "emailDoctor": "Email Doctor", "saveAsPDF": "Save as PDF", "takePhoto": "Take Photo", "chooseFromLibrary": "Choose from Library", "removeImage": "Remove Image", "changeImage": "Change Image", "selectAll": "Select All", "deselectAll": "Deselect All", "reviewAndSave": "Review and Save", "saveSelected": "Save Selected", "sendTestNotification": "Send Test Notification", "enableNotifications": "Enable Notifications", "grantPermission": "Grant Permission", "openSettings": "Open Settings", "viewLabResult": "View Lab Result", "checkResults": "Check Results", "proceed": "Proceed"}, "messages": {"loadingInitialImage": "Loading initial image...", "previewUnavailable": "Preview unavailable.", "noImageSelected": "No Image Selected", "tapToAddImage": "Tap to add medicine image", "imageLoadError": "Failed to load image", "processingImage": "Processing image...", "analyzing": "Analyzing...", "saving": "Saving...", "deleting": "Deleting...", "loading": "Loading...", "deletingSession": "Deleting session...", "savingPDF": "Saving PDF...", "uploadingImage": "Uploading image...", "medicinesFound": "{{count}} medicines found", "noMedicinesFound": "No medicines found in receipt", "partialSaveSuccess": "Some medicines saved successfully", "scanningError": "Error scanning receipt", "permissionRequired": "Permission required", "permissionDenied": "Permission denied", "notificationPermissionRequired": "Notification permission required to send reminders", "cameraPermissionRequired": "Camera permission required to take photos", "libraryPermissionRequired": "Photo library permission required to select images", "autoCompleteWorkplace": "Workplace address auto-completed based on location", "smartFormValidationWarnings": "Smart form detected potential issues"}, "fields": {"name": "Name", "email": "Email", "phone": "Phone Number", "address": "Address", "notes": "Notes", "dosage": "Dosage", "form": "Form", "manufacturer": "Manufacturer", "expirationDate": "Expiration Date", "specialty": "Specialty (Optional)", "licenseNumber": "License Number", "workingHours": "Working Hours", "fullName": "Full Name", "age": "Age", "ageOptional": "Age (Optional)", "bloodType": "Blood Type", "bloodTypeOptional": "Blood Type (Optional)", "allergies": "Allergies", "emergencyContact": "Emergency Contact", "medicalHistory": "Medical History", "testName": "Test Name", "testDate": "Test Date", "laboratory": "Laboratory", "results": "Results", "referenceValues": "Reference Values", "doctor": "Doctor", "patient": "Patient", "frequency": "Frequency", "duration": "Duration", "receiptImage": "Receipt Image", "workplace": "Workplace (Optional)", "city": "City (Optional)", "fee": "Fee (Optional)"}, "validation": {"required": "This field is required", "invalidEmail": "Please enter a valid email address", "invalidPhone": "Please enter a valid phone number", "passwordTooShort": "Password must be at least 8 characters", "selectRequired": "Please make a selection", "invalidDate": "Please select a valid date", "numberRequired": "Please enter a valid number", "minimumAge": "Age must be greater than 0", "maximumAge": "Age must be less than 150", "medicineExpired": "Medicine has expired", "dosageFrequencyMismatch": "Both dosage and frequency should be specified together", "openedAfterExpiration": "Medicine was opened after expiration date", "formHasErrors": "Please correct the errors in the form before saving"}, "units": {"mg": "mg", "g": "g", "ml": "ml", "l": "l", "mcg": "mcg", "iu": "IU", "tablet": "Tablet", "capsule": "Capsule", "syrup": "<PERSON><PERSON><PERSON>", "injection": "Injection", "cream": "Cream", "drops": "Drops", "spray": "Spray", "daily": "Daily", "twiceDaily": "Twice Daily", "threeTimesDaily": "Three Times Daily", "asNeeded": "As Needed", "days": "Days", "weeks": "Weeks", "months": "Months"}, "bloodTypes": {"A+": "A+", "A-": "A-", "B+": "B+", "B-": "B-", "AB+": "AB+", "AB-": "AB-", "O+": "O+", "O-": "O-", "unknown": "Unknown"}, "status": {"active": "Active", "inactive": "Inactive", "expired": "Expired", "expiringSoon": "Expiring Soon", "valid": "<PERSON><PERSON>", "pending": "Pending", "completed": "Completed", "cancelled": "Cancelled", "analyzing": "Analyzing...", "loading": "Loading...", "checking": "Checking...", "aiAnalyzed": "AI Analyzed", "chatActive": "Chat Active"}, "dialogs": {"deleteSession": "Delete Session", "deleteSessionMessage": "Are you sure you want to delete this chat session?", "deleteConfirmation": "Are you sure you want to delete this {{item}}?", "unsavedChanges": "Unsaved Changes", "unsavedChangesMessage": "You have unsaved changes. Are you sure you want to leave?", "confirmAction": "Confirm Action", "selectImageSource": "Select Image Source"}, "headers": {"backButton": "Back", "createAccount": "Create Account", "forgotPassword": "Forgot Password", "verifyEmail": "<PERSON><PERSON><PERSON>", "resetPassword": "Reset Password"}, "imageCapture": {"camera": "Camera", "photoLibrary": "Photo Library", "selectSource": "Select Image Source", "tapToAdd": "Tap to add image", "retakePhoto": "Retake Photo", "usePhoto": "Use This Photo"}, "loading": {"medicineDetails": "Loading medicine details...", "patientDetails": "Loading patient details...", "doctorDetails": "Loading doctor details...", "labResultDetails": "Loading lab result details...", "prescriptionDetails": "Loading prescription details...", "notificationSettings": "Loading notification settings...", "chat": "Loading chat...", "processing": "Processing...", "saving": "Saving...", "uploading": "Uploading...", "analyzing": "Analyzing...", "extracting": "Extracting...", "scanning": "Scanning...", "deleting": "Deleting..."}, "errors": {"loadingMedicineData": "Error loading medicine data.", "loadingPatientData": "Error loading patient data.", "loadingDoctorData": "Error loading doctor data.", "noReceiptImage": "No receipt image was captured. Please try again.", "failedToLoad": "Failed to load data."}, "misc": {"frontSide": "Front Side", "backSide": "Back Side", "addFrontImage": "Add Front Image", "addBackImage": "Add Back Image", "labName": "Lab Name", "resultDate": "Result Date", "verificationCode": "Verification Code", "newPassword": "New Password", "confirmPassword": "Confirm Password", "selectPatientO": "Select Patient O", "addNewPatient": "Add New Patient", "addNewDoctor": "Add New Doctor", "noImageSelected": "No Image Selected", "searchPatients": "Search patients...", "selectDoctor": "Select Doctor", "selectDoctorOptional": "Select doctor (optional)", "searchDoctors": "Search doctors...", "monthlyCheckupExample": "e.g., Monthly Checkup Rx", "age": "Age", "bloodType": "Blood Type", "attachment": "Attachment", "noDate": "No date", "openExternalWebsite": "Open External Website", "externalWebsiteMessage": "You are about to open the lab's website. Patient ID and password (if available on the lab result) will be passed to the next screen for your convenience. Do you want to proceed?", "images": "Image(s)", "patient": "Patient", "doctor": "Doctor", "notes": "Notes", "fee": "Fee", "previewUnavailable": "Preview unavailable.", "tryAgain": "Try again", "loadingImages": "Loading images...", "workingHours": "Working Hours", "noWorkingHoursSet": "No working hours set", "addHours": "Add Hours", "to": "to", "addAnotherTimeSlot": "Add Another Time Slot", "notSet": "Not set", "done": "Done", "attachDocument": "Attach Document", "selectingDocument": "Selecting document...", "tapToAddImage": "Tap to add image", "chooseImageSource": "Choose Image Source", "selectImageSourceMessage": "Select a source for your image.", "takePhoto": "Take Photo", "chooseFromLibrary": "Choose from Library", "processingWithOcr": "Processing with OCR...", "processingImage": "Processing image...", "noDataAvailable": "No data available.", "unexpectedError": "An unexpected error occurred.", "selectAnOption": "Select an option", "selectOption": "Select Option", "createNew": "Create New"}, "tracker": {"title": "Health & Cost Tracker", "filters": "Filters", "selectPatient": "Select Patient", "allPatients": "All Patients", "timeRange": "Time Range", "week": "This Week", "month": "This Month", "3months": "3 Months", "year": "This Year", "all": "All Time", "costStatistics": "Cost Statistics", "healthStatistics": "Health Statistics", "medicines": "Medicines", "doctors": "Doctor <PERSON>", "totalSpent": "Total Spent", "prescriptions": "Prescriptions", "labResults": "Lab Results", "activeMedications": "Active Medications", "topMedicines": "Top Medicine Expenses", "purchases": "purchases"}, "onboarding": {"welcomeToRecepturko": "Welcome to Recepturko", "welcomeSubtitle": "Your AI-powered healthcare companion that helps you understand your medical information like never before", "getStarted": "Get Started", "skipForNow": "Skip for now", "aiAnalysis": "AI Analysis", "labResults": "Lab Results", "medications": "Medications", "next": "Next", "previous": "Previous", "skip": "<PERSON><PERSON>", "aiLabResultsTitle": "AI Lab Results Analysis", "aiLabResultsDescription": "Upload your lab results and get instant AI explanations in simple, easy-to-understand language.", "aiLabResultsFeature": "AI explains complex medical terms and what your numbers mean for your health", "smartMedicationTitle": "Smart Medication Assistant", "smartMedicationDescription": "Scan medicine receipts with OCR technology and get AI-powered explanations about your medications.", "smartMedicationFeature": "AI helps you understand dosages, side effects, and interactions", "aiHealthcareChatTitle": "AI Healthcare Chat", "aiHealthcareChatDescription": "Ask questions about your health records, medications, and lab results to your personal AI assistant.", "aiHealthcareChatFeature": "Context-aware AI that knows your medical history and provides personalized answers", "smartHealthTrackingTitle": "Smart Health Tracking", "smartHealthTrackingDescription": "Keep all your medical information organized with automatic reminders and AI-powered insights.", "smartHealthTrackingFeature": "AI monitors trends and alerts you to important health patterns", "aiPowered": "AI-Powered", "dontShowAgain": "Don't show again"}, "priceCheck": {"checking": "Checking prices...", "noResultsFound": "No prices found for this medicine", "failedToCheckPrices": "Failed to check prices. Please try again.", "locationRequired": "Location access is required to check prices at nearby pharmacies. Please allow location access and try again.", "locationPermanentlyDenied": "Location access was permanently denied. Please enable location permissions in your device settings to check pharmacy prices.", "locationServicesDisabled": "Location services are disabled on your device. Please enable location services in your device settings to check pharmacy prices.", "locationWebNotSupported": "Location-based price checking is not supported on web. Please use the mobile app for this feature.", "locationPermissionDenied": "Location permission was denied. You may need to delete and reinstall the app to reset permissions, then try again.", "openSettings": "Open Settings", "deleteAppReinstall": "Delete & Reinstall", "rateLimitExceeded": "You can only check prices 3 times per minute. Please wait {{time}} seconds before trying again.", "waitTime": "Wait {{time}}s", "resetPermissions": "Reset Permissions", "resetPermissionsInstructions": "To reset location permissions:\n1. Delete this app from your device\n2. Reinstall from the App Store\n3. Allow location permission when prompted", "searchLocation": "Search location", "foundResults": "Found {{count}} price results", "available": "Available", "notAvailable": "Not available", "priceNotAvailable": "Price not available", "viewInPharmacy": "View in Pharmacy", "lastUpdated": "Last updated", "disclaimer": "Prices may vary. Please verify with the pharmacy before purchasing.", "cannotOpenUrl": "Cannot open pharmacy website", "tryDifferentSearch": "Try searching for a different medicine or check your internet connection."}}