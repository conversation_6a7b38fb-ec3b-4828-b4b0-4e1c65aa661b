import {router, Tabs, useSegments } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { TouchableOpacity, View , Alert } from "react-native";
import { Image } from "expo-image";
import { useUser } from '@clerk/clerk-expo';
import { useTheme } from '@react-navigation/native';
import { SearchHeader } from "@/components/common/SearchHeader";
import React, { useState } from 'react';
import HeaderDropDown from "@/components/HeaderDropDown";
import { useImageStore } from '@/store/imageStore';
import { useOcrStore } from '@/store/ocrStore';
import { useTranslation } from '@/hooks/useTranslation';

const TabsLayout = () => {
  const { user } = useUser();
  const { t } = useTranslation();
  const segments = useSegments();
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  
  // OCR functionality for "From Bill" option
  const { setCapturedImageUri } = useOcrStore();
  const imageStore = useImageStore();
  
  const handleMedicineImageCapture = async (type: 'camera' | 'library') => {
    try {
      const result = type === 'camera' 
        ? await imageStore.takePhoto('header-medicine-capture')
        : await imageStore.chooseFromLibrary('header-medicine-capture');
      
      if (result) {
        // Store the captured image URI and navigate to OCR modal
        setCapturedImageUri(result.uri);
        router.push('/modals/modal-medicine-ocr');
      }
    } catch (error) {
      Alert.alert('Image Capture Error', error instanceof Error ? error.message : 'Unknown error');
    }
  };
  
  const handleLabImageCapture = async (type: 'camera' | 'library') => {
    try {
      const result = type === 'camera'
        ? await imageStore.takePhoto('header-lab-capture')
        : await imageStore.chooseFromLibrary('header-lab-capture');
      
      if (result) {
        // Navigate to lab result modal with captured image
        router.push({
          pathname: '/modals/modal-labresult',
          params: { capturedImageUri: result.uri }
        });
      }
    } catch (error) {
      Alert.alert('Image Capture Error', error instanceof Error ? error.message : 'Unknown error');
    }
  };

  const handleSingleMedicineImageCapture = async (type: 'camera' | 'library') => {
    try {
      const result = type === 'camera'
        ? await imageStore.takePhoto('header-single-medicine-capture')
        : await imageStore.chooseFromLibrary('header-single-medicine-capture');
      
      if (result) {
        // Navigate to medicine modal with captured image for OCR
        router.push({
          pathname: '/modals/modal-medicine',
          params: { capturedImageUri: result.uri }
        });
      }
    } catch (error) {
      Alert.alert('Image Capture Error', error instanceof Error ? error.message : 'Unknown error');
    }
  };

  let modalRoute = 'modals/modal-prescription';
  const lastSegment = segments.length > 0 ? segments[segments.length - 1] : 'index';

  switch (lastSegment) {
    case 'index':
      modalRoute = 'modals/modal-prescription';
      break;
    case 'lab-results':
      modalRoute = 'modals/modal-labresult';
      break;
    case '(doctors-tabs)':
      modalRoute = 'modals/modal-doctor';
      break;
    case '(meds-tabs)':
      modalRoute = 'modals/modal-medicine';
      break;
    case 'all-meds':
    case 'meds':
      modalRoute = 'modals/modal-medicine';
      break;
    case 'doctors':
    case 'all-doctors':
      modalRoute = 'modals/modal-doctor';
      break;
    case 'patients':
      modalRoute = 'modals/modal-patient';
      break;
  }

  return (
    <Tabs
      screenOptions={{
        tabBarShowLabel: false,
        headerShown: true,
        headerShadowVisible: false,
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.text,
        tabBarStyle: {
          backgroundColor: theme.colors.card,
          borderTopWidth: 1,
          borderTopColor: theme.colors.border,
        },
        headerStyle: { 
          backgroundColor: theme.colors.card,
        },
        animation: 'shift',
        // headerTitle: () => (
        //   <SearchHeader
        //     searchQuery={searchQuery}
        //     setSearchQuery={setSearchQuery}
        //     theme={theme}
        //     placeholder={t('common.searchPlaceholder')}
        //   />
        // ),
        headerTitle: '',
        headerRight: () => (
          <View className="flex-row gap-4 px-4">
            <Ionicons name="filter-outline" size={24} color={theme.colors.text} onPress={() => router.push("/modals/modal-filter")} />
            <HeaderDropDown title='' 
            icon="add-circle-outline"
            iconColor={theme.colors.text}
            items={
              [{key: 'name', title: t('actions.manual'), icon: 'plus'},
              {key: 'camera', title: t('actions.fromCamera'), icon: 'camera'}, // TODO: hide on doctors-tabs and patients-tabs
              {key: 'gallery', title: t('actions.fromGallery'), icon: 'folder'}, // TODO: hide on doctors-tabs and patients-tabs
              {key: 'bill', title: t('actions.fromBill'), icon: 'receipt'}, // TODO: Show only on meds-tabs
                ]}
              onSelect={(key) => {
                if (key === 'name') {
                  router.push(modalRoute as any)
                } else if (key === 'camera') {
                  // For lab-results tab, capture image and navigate to modal
                  if (lastSegment === 'lab-results') {
                    handleLabImageCapture('camera');
                  }
                  // For meds tabs, capture single medicine image and navigate to modal
                  else if (lastSegment === '(meds-tabs)' || lastSegment === 'all-meds' || lastSegment === 'meds') {
                    handleSingleMedicineImageCapture('camera');
                  }
                } else if (key === 'gallery') {
                  // For lab-results tab, select image and navigate to modal
                  if (lastSegment === 'lab-results') {
                    handleLabImageCapture('library');
                  }
                  // For meds tabs, select single medicine image and navigate to modal
                  else if (lastSegment === '(meds-tabs)' || lastSegment === 'all-meds' || lastSegment === 'meds') {
                    handleSingleMedicineImageCapture('library');
                  }
                } else if (key === 'bill') {
                  // Handle "From Bill" option - show camera/gallery selection
                  Alert.alert(
                    t('medicines.scanReceipt'),
                    t('medicines.chooseCapture'),
                    [
                      {
                        text: t('medicines.camera'),
                        onPress: () => handleMedicineImageCapture('camera'),
                      },
                      {
                        text: t('medicines.photoLibrary'),
                        onPress: () => handleMedicineImageCapture('library'),
                      },
                      {
                        text: t('common.cancel'),
                        style: 'cancel',
                      },
                    ]
                  );
                }
              }}
            />
            
            <Ionicons name="chatbubble-outline" size={24} color={theme.colors.text} onPress={() => router.push("/(protected)/(ai-chat)/welcome")} />
          </View>
        ),
        headerLeft: () => (
          <TouchableOpacity className="px-4" onPress={() => router.push("/settings")}>
            <Image source={{uri: user?.imageUrl}} style={{width: 32, height: 32, borderRadius: 16}} />
          </TouchableOpacity>
        ),
      }}
    >
      <Tabs.Screen
        name="tracker"
        options={{
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="analytics-outline" color={color} size={size} />
          ),
        }}
      />
      <Tabs.Screen
        name="index"
        options={{
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="document-text-outline" color={color} size={size} />
          ),
        }}
      />
      <Tabs.Screen
        name="lab-results"
        options={{
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="flask-outline" color={color} size={size} />
          ),
        }}
      />
      <Tabs.Screen
        name="(meds-tabs)"
        options={{
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="medkit-outline" color={color} size={size} />
          ),
        }}
      />
      <Tabs.Screen
        name="notifications"
        options={{
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="calendar-outline" color={color} size={size} />
          ),
        }}
      />
      
      <Tabs.Screen
        name="(doctors-tabs)"
        options={{
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="people-outline" color={color} size={size} />
          ),
        }}
      />
    </Tabs>
  );
}

export default TabsLayout;
