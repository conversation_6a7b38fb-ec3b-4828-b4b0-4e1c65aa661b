import { Text, View, ActivityIndicator, useColorScheme, Modal, TouchableOpacity, useWindowDimensions } from 'react-native'
import  { useMemo, useContext, useState, useLayoutEffect } from 'react'
import { CalendarProvider, AgendaList, ExpandableCalendar, AgendaListProps, CalendarContext } from 'react-native-calendars'
import { MarkedDates , Theme } from 'react-native-calendars/src/types'
import { useMedicines, MedicineWithRelations } from '@/hooks/entities/useMedicines'
import { format } from 'date-fns'
import colors from 'tailwindcss/colors';
import { useMedicineExpirationNotifications } from '@/hooks/useMedicineExpirationNotifications';
import Card from '@/components/ui/Card';
import { useTranslation } from '@/hooks/useTranslation';
import { Stack, useNavigation } from 'expo-router';
import { useTheme } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import MedicineCard from '@/components/medications/MedicineCard';

interface AgendaItem {
  title: string
  data: MedicineWithRelations[]
}

// Separate component to consume context, ensuring it re-renders when date changes
function FilteredAgenda() {
  const { t } = useTranslation()
  const { useFetchMedicines } = useMedicines()
  const { data: medicines, isLoading, isError, error } = useFetchMedicines()
  const context = useContext(CalendarContext) // Get the full context
  const selectedDate = context.date // Get the currently selected date string (YYYY-MM-DD) from context
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Process all medicine data (memoized based on medicines)
  const allAgendaItems = useMemo(() => {
    const items: { [key: string]: MedicineWithRelations[] } = {}
    if (medicines) {
      medicines.forEach((med) => {
        if (med.expiration_date) {
          const expirationDateString = format(new Date(med.expiration_date), 'yyyy-MM-dd')
          if (!items[expirationDateString]) {
            items[expirationDateString] = []
          }
          items[expirationDateString].push(med)
        }
      })
    }
    // Convert grouped items to AgendaList format and sort by date
    const agendaSections: AgendaItem[] = Object.keys(items)
      .sort() // Sort dates chronologically
      .map(d => ({
        title: d,
        data: items[d],
      }))
    return agendaSections
  }, [medicines])

  // Filter items based on the context date (memoized based on allAgendaItems and context date)
  const filteredAgendaItems = useMemo(() => {
    if (!selectedDate) return [] // Handle case where date might be initially null
    return allAgendaItems.filter(section => section.title === selectedDate)
  }, [allAgendaItems, selectedDate])

  const renderItem: AgendaListProps['renderItem'] = ({ item }) => {
    return (
      <View className="my-2 mr-4">
        <MedicineCard medicine={item} />
      </View>
    );
  }

  if (isLoading) {
    return (
      <View className="flex-1 justify-center items-center mt-10 bg-background dark:bg-neutral-900">
        <ActivityIndicator size="large" className="text-primary" />
        <Text className="mt-2 text-muted-foreground dark:text-neutral-400">{t('medicines.loadingMedicines')}</Text>
      </View>
    )
  }

  if (isError) {
    return (
      <View className="flex-1 justify-center items-center p-4 mt-10 bg-background dark:bg-neutral-900">
        <Text className="text-destructive dark:text-red-400">{t('medicines.errorLoadingMedicines')}:</Text>
        <Text className="text-destructive dark:text-red-400">{error?.message}</Text>
      </View>
    )
  }

  return (
    <AgendaList
      sections={filteredAgendaItems} // Use the filtered items
      renderItem={renderItem}
      sectionStyle={{ marginBottom: 10 }}
      style={{ backgroundColor: isDark ? colors.neutral[900] : colors.white }}
      ListEmptyComponent={
        <View className="flex-1 justify-center items-center mt-10 p-4">
          {/* Check if there are items for *any* date before saying none found */}
          {allAgendaItems.length > 0 ? (
            <Text className="text-muted-foreground dark:text-neutral-400 text-center">
              {t('notifications.noExpiringMedicinesOnDate', { date: format(new Date(selectedDate || Date.now()), 'MMM d, yyyy') })}
            </Text>
          ) : (
            <Text className="text-muted-foreground dark:text-neutral-400 text-center">{t('notifications.noExpiringMedicinesFound')}</Text>
          )}
        </View>
      }
    />
  )
}

function NotificationBell() {
  const { totalExpiringCount } = useMedicineExpirationNotifications();
  const [modalVisible, setModalVisible] = useState(false);
  const navigation = useNavigation();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { notifications, hasExpiredMedicines } = useMedicineExpirationNotifications();
  const { t } = useTranslation();
  const { width } = useWindowDimensions();
  const theme = useTheme();

  // Define theme colors based on scheme
  const primaryColor = colors.blue[600];
  const destructiveColor = colors.red[500];
  const warningColor = colors.orange[500];

  const handleOpenModal = () => {
    if (totalExpiringCount > 0) {
      setModalVisible(true);
    }
  };

  return (
    <>
      <TouchableOpacity onPress={handleOpenModal} className="mr-4">
        <Ionicons 
          name="notifications-outline" 
          size={24} 
          color={theme.colors.text} 
        />
        {totalExpiringCount > 0 && (
          <View className="absolute -top-1 -right-1 bg-red-500 rounded-full w-4 h-4 justify-center items-center">
            <Text className="text-white text-xs font-bold">{totalExpiringCount}</Text>
          </View>
        )}
      </TouchableOpacity>
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View className="flex-1 justify-center items-center bg-black/50">
          <View 
            className="m-4 bg-card dark:bg-neutral-800 rounded-2xl p-6 shadow-xl border border-border dark:border-neutral-700"
            style={{ width: width * 0.9 }}
          >
            <TouchableOpacity onPress={() => setModalVisible(false)} className="absolute top-4 right-4 z-10">
              <Ionicons name="close-circle" size={28} color={theme.colors.text} />
            </TouchableOpacity>
            
            <Text className="text-xl font-bold mb-4 text-card-foreground dark:text-neutral-100">{t('notifications.title')}</Text>

            {hasExpiredMedicines() && (
              <Card 
                title="⚠️ Urgent: Expired Medicines"
                style={{
                  backgroundColor: destructiveColor + '15',
                  borderColor: destructiveColor,
                  borderWidth: 2,
                  marginBottom: 12,
                }}
              >
                <Text className="text-sm font-semibold" style={{ color: destructiveColor }}>
                  You have expired medicines that need immediate attention. Please check and dispose of them safely.
                </Text>
              </Card>
            )}
            
            {notifications.map((notification, index) => (
              <Card 
                key={index}
                title={notification.title}
                style={{
                  backgroundColor: notification.priority === 'high' ? (isDark ? colors.red[900] : colors.red[50]) : 
                                  notification.priority === 'medium' ? (isDark ? colors.orange[900] : colors.orange[50]) : 
                                  (isDark ? colors.blue[900] : colors.blue[50]),
                  borderColor: notification.priority === 'high' ? destructiveColor : 
                              notification.priority === 'medium' ? warningColor : 
                              primaryColor,
                  borderWidth: 1,
                  marginBottom: 8,
                }}
              >
                  <Text className={`text-sm mt-1 ${
                    notification.priority === 'high' ? 'text-red-700 dark:text-red-300' : 
                    notification.priority === 'medium' ? 'text-orange-700 dark:text-orange-300' : 
                    'text-blue-700 dark:text-blue-300'
                  }`}>
                    {notification.message}
                  </Text>
                  <Text className={`text-xs mt-2 ${
                    notification.priority === 'high' ? 'text-red-600 dark:text-red-400' : 
                    notification.priority === 'medium' ? 'text-orange-600 dark:text-orange-400' : 
                    'text-blue-600 dark:text-blue-400'
                  }`}>
                    {notification.medicines.map(med => med.name).join(', ')}
                  </Text>
              </Card>
            ))}
          </View>
        </View>
      </Modal>
    </>
  );
}

export default function NotificationsScreen() {
  const { useFetchMedicines } = useMedicines()
  const { data: medicines } = useFetchMedicines() // Fetch here mainly for marked dates
  const { notifications, totalExpiringCount, hasExpiredMedicines } = useMedicineExpirationNotifications();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { t } = useTranslation();
  const navigation = useNavigation();

  const today = new Date()
  const todayString = format(today, 'yyyy-MM-dd')

  // Add the notification bell to the header
  useLayoutEffect(() => {
    navigation.setOptions({
      headerRight: () => <NotificationBell />,
    });
  }, [navigation]);

  // Define theme colors based on scheme
  const primaryColor = colors.blue[600];
  const primaryForegroundColor = colors.neutral[50];
  const foregroundColor = isDark ? colors.neutral[100] : colors.neutral[950];
  const mutedForegroundColor = isDark ? colors.neutral[400] : colors.neutral[500];
  const backgroundColor = isDark ? colors.neutral[900] : colors.white;
  const cardBackgroundColor = isDark ? colors.neutral[800] : colors.white;
  const borderColor = isDark ? colors.neutral[700] : colors.neutral[200];
  const destructiveColor = colors.red[500];
  const warningColor = colors.orange[500]; // For expiration dot

  // Calendar theme object with enhanced styling
  const calendarTheme: Theme = {
    calendarBackground: backgroundColor,
    dayTextColor: foregroundColor,
    textDisabledColor: mutedForegroundColor,
    monthTextColor: foregroundColor,
    textSectionTitleColor: mutedForegroundColor,
    arrowColor: primaryColor,
    todayTextColor: primaryColor,
    selectedDayBackgroundColor: primaryColor,
    selectedDayTextColor: primaryForegroundColor,
    agendaKnobColor: primaryColor,
    // Enhanced styling using previously unused variables
    backgroundColor: cardBackgroundColor,
    textDayFontWeight: '500',
    textMonthFontWeight: '600',
    textDayHeaderFontWeight: '500',
  };

  // Calculate marked dates (only needs medicines data)
  const markedDates = useMemo(() => {
    const marked: MarkedDates = {}
    if (medicines) {
      medicines.forEach((med) => {
        if (med.expiration_date) {
          const expirationDateString = format(new Date(med.expiration_date), 'yyyy-MM-dd')
          const isExpired = new Date(med.expiration_date) < new Date()
          
          marked[expirationDateString] = {
            marked: true,
            dotColor: isExpired ? destructiveColor : warningColor,
            // Enhanced visual feedback for expired medicines
            ...(isExpired && {
              customStyles: {
                container: {
                  backgroundColor: destructiveColor + '20', // Semi-transparent red
                  borderRadius: 6,
                },
                text: {
                  color: destructiveColor,
                  fontWeight: 'bold',
                },
              },
            }),
          }
        }
      })
    }
    // Mark today's date
    marked[todayString] = {
      ...(marked[todayString] || {}),
      selected: true,
      // selectedColor is handled by theme.selectedDayBackgroundColor
      // selectedTextColor is handled by theme.selectedDayTextColor
    }
    return marked
  }, [medicines, todayString, warningColor, destructiveColor])

  return (
    <View className="flex-1 bg-background dark:bg-neutral-900">
      <Stack.Screen options={{ title: t('notifications.title') }} />
      {/* Remove the old notification summary */}
      
      {/* CalendarProvider wraps both Calendar and the new Agenda component */}
      <CalendarProvider
        date={todayString}
        showTodayButton
        theme={{
          todayButtonTextColor: primaryColor,
        }}
      >
        <ExpandableCalendar
          markedDates={markedDates}
          theme={calendarTheme}
          // Enhanced calendar styling
          style={{
            backgroundColor: cardBackgroundColor,
            borderBottomWidth: 1,
            borderBottomColor: borderColor,
          }}
        />
        {/* Render the separate component which uses the context */}
        <FilteredAgenda />
      </CalendarProvider>
    </View>
  )
}