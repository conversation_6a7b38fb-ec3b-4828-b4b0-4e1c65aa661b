import React from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { FlashList } from '@shopify/flash-list';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@react-navigation/native';
import { useAllMedicines, AllMedicine } from '@/hooks/entities/useAllMedicines';
import { Button } from '@/components/ui/Button';
import { useTranslation } from '@/hooks/useTranslation';

// Simple Card component for displaying medicine information
interface AllMedicineCardProps {
  medicine: AllMedicine;
  onAdd: () => void;
}

const AllMedicineCard: React.FC<AllMedicineCardProps> = ({ medicine, onAdd }) => {
  const { t } = useTranslation();
  
  return (
    <View className="bg-card dark:bg-neutral-800 p-4 rounded-lg shadow mb-4">
      <Text className="text-lg font-semibold text-foreground dark:text-neutral-100">{medicine.name}</Text>
      {medicine.description && <Text className="text-sm text-muted-foreground dark:text-neutral-300 mt-1">{medicine.description}</Text>}
      {/* Optionally display other fields like dosage, etc. if needed in the card */}
      {/* For example:
      {medicine.dosage_amount && medicine.dosage_unit && 
        <Text className="text-xs text-muted-foreground dark:text-neutral-400 mt-1">
          Dosage: {medicine.dosage_amount} {medicine.dosage_unit}
        </Text>
      }
      */}
      <View className="mt-3">
        <Button title={t('medicines.addToMyMedications')} onPress={onAdd} variant="outline" />
      </View>
    </View>
  );
};

const AllMedsScreen = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const { useFetchAllMedicines } = useAllMedicines();
  const { data: allMedicines, isLoading, isError, refetch, error } = useFetchAllMedicines();

  const handleAddMedicine = (medicine: AllMedicine) => {
    router.push({
      pathname: '/modals/modal-medicine',
      params: {
        initialName: medicine.name,
        initialDescription: medicine.description,
        initialPrice: medicine.price?.toString(),
        initialDosageAmount: medicine.dosage_amount?.toString(),
        initialDosageUnit: medicine.dosage_unit,
        initialFrequencyAmount: medicine.frequency_amount?.toString(),
        initialFrequencyUnit: medicine.frequency_unit,
        initialDurationAmount: medicine.duration_amount?.toString(),
        initialDurationUnit: medicine.duration_unit,
        initialMedId: medicine.id,
      },
    });
  };

  if (isLoading) {
    return (
      <View className="flex-1 justify-center items-center bg-background dark:bg-neutral-900">
        <ActivityIndicator size="large" className="text-primary" />
        <Text className="mt-4 text-muted-foreground dark:text-neutral-400">{t('medicines.loadingMedicines')}</Text>
      </View>
    );
  }

  if (isError) {
    console.error("Error fetching all medicines:", error);
    return (
      <View className="flex-1 justify-center items-center p-4 bg-background dark:bg-neutral-900">
        <Ionicons name="alert-circle-outline" size={48} color={theme.colors.notification} />
        <Text className="text-destructive dark:text-red-400 mt-4 mb-4 text-center text-lg font-medium">
          {t('medicines.failedToLoad')}
        </Text>
        <Text className="text-muted-foreground dark:text-neutral-400 mb-6 text-center px-4">
          {error?.message || t('common.error')}
        </Text>
        <Button title={t('common.tryAgain')} onPress={() => refetch()} variant="primary" />
      </View>
    );
  }

  if (!allMedicines || allMedicines.length === 0) {
    return (
      <View className="flex-1 justify-center items-center p-6 bg-background dark:bg-neutral-900">
        <Ionicons name="medkit-outline" size={54} color={theme.colors.text} />
        <Text className="text-foreground dark:text-neutral-100 mt-5 mb-2 text-center font-semibold text-xl">
          {t('medicines.noMedicines')}
        </Text>
        <Text className="text-muted-foreground dark:text-neutral-400 mb-6 text-center px-4 leading-relaxed">
          {t('medicines.noMedicinesMessage')}
        </Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-background dark:bg-neutral-900">
      <FlashList
        data={allMedicines}
        renderItem={({ item }) => (
          <AllMedicineCard
            medicine={item}
            onAdd={() => handleAddMedicine(item)}
          />
        )}
        estimatedItemSize={120} // Adjust based on AllMedicineCard expected height
        contentContainerClassName="p-4"
      />
    </View>
  );
};

export default AllMedsScreen;