import React from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { FlashList } from '@shopify/flash-list';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@react-navigation/native';
import { useMedicines } from '@/hooks/entities/useMedicines'; // Import the medicine hook
import MedicineCard from '@/components/medications/MedicineCard'; // Import the medicine card
import { Button } from '@/components/ui/Button'; // Import Button
import { useTranslation } from '@/hooks/useTranslation';
import { useRateLimitStore } from '@/store/rateLimitStore';

/**
 * User Medicines screen showing a list of the user's added medicines.
 */
export default function UserMedicinesScreen() {
  const { t } = useTranslation();
  const theme = useTheme();
  const { useFetchMedicines } = useMedicines();
  const { data: medicines, isLoading, isError, refetch, error } = useFetchMedicines();
  const { priceCheckingMedicine } = useRateLimitStore();

  // Navigate to the modal screen for viewing/editing a specific medicine
  const handlePressMedicine = (id: string) => {
    // Push the modal route with the medicine id parameter
    router.push({ pathname: '/modals/modal-medicine', params: { id } });
  };

  // Navigate to the modal screen for creating a new medicine
  const handleCreate = () => {
    // Push the modal route without params
    router.push('/modals/modal-medicine'); 
  };

  // Show loading state
  if (isLoading) {
    return (
      <View className="flex-1 justify-center items-center bg-background dark:bg-neutral-900">
        <ActivityIndicator size="large" className="text-primary" />
        <Text className="mt-4 text-muted-foreground dark:text-neutral-400">{t('medicines.loadingMedicines')}</Text>
      </View>
    );
  }

  // Show error state
  if (isError) {
    // Log the full error object to the console
    console.error("Error fetching medicines:", error);
    return (
      <View className="flex-1 justify-center items-center p-4 bg-background dark:bg-neutral-900">
        <Ionicons name="alert-circle-outline" size={48} color={theme.colors.notification} />
        <Text className="text-destructive dark:text-red-400 mt-4 mb-4 text-center text-lg font-medium">
          {t('medicines.failedToLoad')}
        </Text>
        {/* Display the actual error message */}
        <Text className="text-muted-foreground dark:text-neutral-400 mb-6 text-center px-4">
          {error?.message || t('common.error')}
        </Text>
        <Button title={t('common.tryAgain')} onPress={() => refetch()} variant="primary" />
      </View>
    );
  }

  // Show empty state
  if (!medicines || medicines.length === 0) {
    return (
      <View className="flex-1 justify-center items-center p-6 bg-background dark:bg-neutral-900">
        <Ionicons name="medkit-outline" size={54} color={theme.colors.text} />
        <Text className="text-foreground dark:text-neutral-100 mt-5 mb-2 text-center font-semibold text-xl">
          {t('medicines.noMedicines')}
        </Text>
        <Text className="text-muted-foreground dark:text-neutral-400 mb-6 text-center px-4 leading-relaxed">
          {t('medicines.noMedicinesMessage')}
        </Text>
        <Button title={t('medicines.addFirstMedicine')} onPress={handleCreate} variant="primary" />
      </View>
    );
  }

  // Main list view with 2-card horizontal layout
  return (
    <View className="flex-1 bg-background dark:bg-neutral-900">
      {priceCheckingMedicine ? (
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" className="text-primary" />
          <Text className="mt-4 text-muted-foreground dark:text-neutral-400">
            {t('priceCheck.checking')}
          </Text>
        </View>
      ) : (
        <FlashList
          data={medicines}
          renderItem={({ item }) => (
            <MedicineCard
              medicine={item}
              onPress={() => handlePressMedicine(item.id)}
            />
          )}
          keyExtractor={(item) => item.id}
          numColumns={2}
          contentContainerStyle={{ padding: 4 }}
          showsVerticalScrollIndicator={false}
          estimatedItemSize={300}
        />
      )}
    </View>
  );
}