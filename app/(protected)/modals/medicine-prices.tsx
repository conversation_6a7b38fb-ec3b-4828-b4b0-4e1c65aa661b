import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, Alert, Pressable, Linking } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { PriceResult } from '@/hooks/useCheckMedicinePrices';
import { useTranslation } from '@/hooks/useTranslation';
import { Button } from '@/components/ui/Button';

/**
 * MEDICINE PRICES MODAL
 * 
 * This modal displays price results for a specific medicine from Bulgarian pharmacies.
 * It receives fresh price data from the parent component and displays it.
 * 
 * FEATURES:
 * - Displays price results with pharmacy names, prices, and availability
 * - Shows search location and timestamp for context
 * - Provides direct links to pharmacy websites
 * - Handles empty results gracefully
 * - Follows app's design patterns and styling
 * 
 * WORKFLOW:
 * 1. Receives medicine name and price data from navigation params
 * 2. Displays results in a scrollable list
 * 3. Allows users to open pharmacy websites directly
 * 4. Shows appropriate messaging for empty results
 */
export default function MedicinePricesModal() {
  // Get medicine name and price data from navigation parameters
  const { medicineName, priceData } = useLocalSearchParams<{
    medicineName: string;
    medicineId: string;
    priceData?: string; // JSON string of price results
  }>();
  
  const theme = useTheme();
  const { t } = useTranslation();
  
  // State for displaying price results
  const [priceResults, setPriceResults] = useState<PriceResult[]>([]);
  const [searchLocation, setSearchLocation] = useState<string>('');
  const [timestamp, setTimestamp] = useState<string>('');

  // Load price data when component mounts or data changes
  useEffect(() => {
    if (priceData) {
      try {
        const parsed = JSON.parse(priceData);
        setPriceResults(parsed.results || []);
        setSearchLocation(parsed.searchLocation || '');
        setTimestamp(parsed.timestamp || '');
      } catch (error) {
        console.error('Failed to parse price data:', error);
      }
    }
  }, [priceData]);

  /**
   * Handle opening pharmacy URLs in the browser
   * Uses Linking API to open external URLs safely
   */
  const handleOpenUrl = async (url: string) => {
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        Alert.alert(t('common.error'), t('priceCheck.cannotOpenUrl'));
      }
    } catch (error) {
      console.error('Failed to open URL:', error);
      Alert.alert(t('common.error'), t('priceCheck.cannotOpenUrl'));
    }
  };

  /**
   * Format price for display
   * Shows formatted price with currency or "not available" message
   */
  const formatPrice = (price: number | null, currency: string) => {
    if (price === null) return t('priceCheck.priceNotAvailable');
    return `${price.toFixed(2)} ${currency}`;
  };

  /**
   * Format timestamp for display
   * Converts ISO timestamp to user-friendly format
   */
  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleString();
    } catch {
      return timestamp;
    }
  };

  /**
   * Get availability text for display
   * Returns localized availability status
   */
  const getAvailabilityText = (availability: boolean) => {
    return availability ? t('priceCheck.available') : t('priceCheck.notAvailable');
  };

  /**
   * Get availability color class
   * Returns appropriate color for availability status
   */
  const getAvailabilityColor = (availability: boolean) => {
    return availability ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';
  };

  if (!medicineName) {
    return (
      <SafeAreaView className="flex-1 bg-white dark:bg-neutral-900">
        <View className="flex-1 justify-center items-center">
          <Text className="text-red-500 text-lg">{t('common.error')}</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-white dark:bg-neutral-900">
      {/* Header */}
      <View className="px-4 py-3 border-b border-gray-200 dark:border-neutral-700">
        <View className="flex-row items-center justify-between">
          <View className="flex-1">
            <Text className="text-lg font-semibold text-gray-900 dark:text-white" numberOfLines={1}>
              {medicineName}
            </Text>
            <Text className="text-sm text-gray-500 dark:text-gray-400">
              {t('priceCheck.searchLocation')}: {searchLocation}
            </Text>
          </View>
          <Pressable
            onPress={() => router.back()}
            className="p-2 rounded-full bg-gray-100 dark:bg-neutral-800"
          >
            <Ionicons name="close" size={24} color={theme.colors.text} />
          </Pressable>
        </View>
      </View>

      {/* Content */}
      <ScrollView className="flex-1 px-4 py-4">
        {priceResults.length === 0 ? (
          <View className="flex-1 justify-center items-center py-12">
            <Ionicons name="search-outline" size={48} color={theme.colors.text} />
            <Text className="text-lg font-medium text-gray-900 dark:text-white mt-4">
              {t('priceCheck.noResultsFound')}
            </Text>
            <Text className="text-gray-500 dark:text-gray-400 text-center mt-2">
              {t('priceCheck.tryDifferentSearch')}
            </Text>
          </View>
        ) : (
          <View className="space-y-4">
            <Text className="text-base font-medium text-gray-700 dark:text-gray-300">
              {t('priceCheck.foundResults', { count: priceResults.length })}
            </Text>
            
            {priceResults.map((result, index) => (
              <View
                key={index}
                className="bg-gray-50 dark:bg-neutral-800 rounded-lg p-4 border border-gray-200 dark:border-neutral-700"
              >
                <View className="flex-row items-start justify-between mb-3">
                  <View className="flex-1">
                    <Text className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                      {result.pharmacy_name}
                    </Text>
                    <Text className={`text-sm font-medium ${getAvailabilityColor(result.availability)}`}>
                      {getAvailabilityText(result.availability)}
                    </Text>
                  </View>
                  <View className="items-end">
                    <Text className="text-xl font-bold text-gray-900 dark:text-white">
                      {formatPrice(result.price, result.currency)}
                    </Text>
                  </View>
                </View>
                
                <Button
                  title={t('priceCheck.viewInPharmacy')}
                  onPress={() => handleOpenUrl(result.url)}
                  variant="outline"
                  icon="link"
                  style={{ marginTop: 8 }}
                />
              </View>
            ))}
          </View>
        )}
      </ScrollView>

      {/* Footer */}
      <View className="px-4 py-3 border-t border-gray-200 dark:border-neutral-700">
        <Text className="text-xs text-gray-500 dark:text-gray-400 text-center">
          {t('priceCheck.lastUpdated')}: {formatTimestamp(timestamp)}
        </Text>
        <Text className="text-xs text-gray-500 dark:text-gray-400 text-center mt-1">
          {t('priceCheck.disclaimer')}
        </Text>
      </View>
    </SafeAreaView>
  );
}