import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useUser, useClerk } from '@clerk/clerk-expo';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@react-navigation/native';
import { useSupabaseClient } from '@/hooks/useSupabaseClient';
import MedicineNotificationSettings from '@/components/settings/MedicineNotificationSettings';
import { LanguageSettings } from '@/components/settings/LanguageSettings';
import { useTranslation } from '@/hooks/useTranslation';
import { useOnboardingStore } from '@/store/onboardingStore';
import Animated, { useSharedValue, useAnimatedStyle, withTiming, Easing } from 'react-native-reanimated';

export default function Settings() {
  const { t } = useTranslation();
  const { resetToShowOnboarding } = useOnboardingStore();
  const theme = useTheme();
  const { user } = useUser();
  const { signOut } = useClerk();
  const { supabase } = useSupabaseClient();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [supabaseEmail, setSupabaseEmail] = useState<string | null>(null);
  const [isFetchingEmail, setIsFetchingEmail] = useState(true);
  const [isNotificationsExpanded, setIsNotificationsExpanded] = useState(false);
  const [isLanguageExpanded, setIsLanguageExpanded] = useState(false);

  // Reanimated shared values
  const notificationsOpacity = useSharedValue(0);
  const notificationsHeight = useSharedValue(0);
  const languageOpacity = useSharedValue(0);
  const languageHeight = useSharedValue(0);

  // Animation config
  const animationConfig = {
    duration: 300,
    easing: Easing.out(Easing.ease),
  };

  // Animated styles
  const notificationsAnimatedStyle = useAnimatedStyle(() => ({
    opacity: notificationsOpacity.value,
    maxHeight: notificationsHeight.value,
    overflow: 'hidden' as const,
  }));

  const languageAnimatedStyle = useAnimatedStyle(() => ({
    opacity: languageOpacity.value,
    maxHeight: languageHeight.value,
    overflow: 'hidden' as const,
  }));

  // Toggle functions with reanimated
  const toggleNotifications = () => {
    const newExpanded = !isNotificationsExpanded;
    setIsNotificationsExpanded(newExpanded);
    
    if (newExpanded) {
      notificationsHeight.value = withTiming(1000, animationConfig);
      notificationsOpacity.value = withTiming(1, animationConfig);
    } else {
      notificationsHeight.value = withTiming(0, animationConfig);
      notificationsOpacity.value = withTiming(0, animationConfig);
    }
  };

  const toggleLanguage = () => {
    const newExpanded = !isLanguageExpanded;
    setIsLanguageExpanded(newExpanded);
    
    if (newExpanded) {
      languageHeight.value = withTiming(600, animationConfig);
      languageOpacity.value = withTiming(1, animationConfig);
    } else {
      languageHeight.value = withTiming(0, animationConfig);
      languageOpacity.value = withTiming(0, animationConfig);
    }
  };

  const handleSignOut = async () => {
    try {
      setIsLoading(true);
      await signOut();
      // Router will automatically redirect to sign-in due to auth check in protected layout
    } catch (error) {
      console.error('Error signing out:', error);
      Alert.alert(t('common.error'), t('alerts.failedToSignOut'));
    } finally {
      setIsLoading(false);
    }
  };

  const confirmDeleteAccount = () => {
    Alert.alert(
      t('settings.deleteAccount'),
      t('settings.deleteAccountConfirmation'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        { text: t('common.delete'), style: 'destructive', onPress: handleDeleteAccount }
        //TODO: Ensure user cannot interact with the app during deletion
      ],
      { cancelable: true }
    );
  };

  const handleDeleteAccount = async () => {
    try {
      setIsLoading(true);
      // Delete the user account
      await user?.delete();
      // The auth state will change automatically and redirect to sign-in
    } catch (error) {
      console.error('Error deleting account:', error);
      Alert.alert(t('common.error'), t('alerts.failedToDeleteAccount'));
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!supabase || !user?.id) {
      if(!user?.id) setIsFetchingEmail(false);
      return;
    }

    const fetchProfile = async () => {
      setIsFetchingEmail(true);
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('email')
          .eq('id', user.id)
          .single();

        if (error) {
          if (error.code === 'PGRST116') {
             console.warn('No profile found for user in Supabase:', user.id);
             setSupabaseEmail(t('settings.profileNotFound')); 
          } else {
             throw error;
          }
        } else if (data) {
          setSupabaseEmail(data.email);
        }
      } catch (error) {
        console.error('Error fetching Supabase profile email:', error);
        setSupabaseEmail(t('settings.errorFetchingEmail'));
      } finally {
        setIsFetchingEmail(false);
      }
    };

    fetchProfile();

  }, [supabase, user?.id, t]);

  return (
    <>
    <Stack.Screen options={{
      headerTitle: t('settings.title'),
      headerBackVisible: true,
      headerBackTitle: t('common.back'),
      animation: 'slide_from_left',
      // Header styles like background/tint should be inherited from parent if set there
      // Or uncomment and theme explicitly:
      // headerStyle: { backgroundColor: isDark ? colors.neutral[900] : colors.white },
      // headerTintColor: isDark ? colors.neutral[100] : colors.neutral[950],
      // headerTitleStyle: { color: isDark ? colors.neutral[100] : colors.neutral[950] },
    }} />
    
        <Animated.ScrollView className="flex-1 bg-background dark:bg-neutral-900">
          {/* User Profile Section */}
          <View className="p-6 mb-4 border-b border-border dark:border-neutral-700">
            <View className="items-center mb-4">
              <View className="w-20 h-20 bg-primary/10 dark:bg-primary/20 rounded-full items-center justify-center mb-3">
                <Text className="text-2xl font-bold text-primary dark:text-primary">
                  {user?.firstName?.charAt(0) || supabaseEmail?.charAt(0)?.toUpperCase() || '?'}
                </Text>
              </View>
              <Text className="text-lg font-bold text-foreground dark:text-neutral-100">
                {user?.firstName ? `${user.firstName} ${user.lastName || ''}` : t('settings.user')}
              </Text>
              <Text className="text-muted-foreground dark:text-neutral-400">
                {isFetchingEmail ? t('common.loading') : (supabaseEmail || t('settings.noEmailFound'))}
              </Text>
            </View>
          </View>

          {/* Settings Options */}
          <Animated.View className="px-6 mb-6">
            <Text className="text-lg font-bold mb-2 text-foreground dark:text-neutral-100">{t('settings.account')}</Text>
          
            <TouchableOpacity 
              className="flex-row items-center py-4 border-b border-border dark:border-neutral-700"
              onPress={toggleNotifications}
            >
              <Ionicons name="notifications-outline" size={24} color={theme.colors.text} />
              <Text className="ml-3 text-base text-foreground dark:text-neutral-100">{t('navigation.notifications')}</Text>
              <Ionicons 
                name={isNotificationsExpanded ? "chevron-down" : "chevron-forward"} 
                size={20} 
                color={theme.colors.text} 
                style={{marginLeft: 'auto'}} 
              />
            </TouchableOpacity>
            
            {/* Medicine Notification Settings - Collapsible with Animation */}
            <Animated.View style={notificationsAnimatedStyle}>
              <View className="pl-6 py-2">
                <MedicineNotificationSettings />
              </View>
            </Animated.View>
          
          </Animated.View>
          
          {/* Language Settings */}
          <Animated.View className="px-6 mb-6">
            <Text className="text-lg font-bold mb-2 text-foreground dark:text-neutral-100">{t('settings.preferences')}</Text>
            
            <TouchableOpacity 
              className="flex-row items-center py-4 border-b border-border dark:border-neutral-700"
              onPress={toggleLanguage}
            >
              <Ionicons name="language-outline" size={24} color={theme.colors.text} />
              <Text className="ml-3 text-base text-foreground dark:text-neutral-100">{t('settings.language')}</Text>
              <Ionicons 
                name={isLanguageExpanded ? "chevron-down" : "chevron-forward"} 
                size={20} 
                color={theme.colors.text} 
                style={{marginLeft: 'auto'}} 
              />
            </TouchableOpacity>
            
            {/* Language Settings - Collapsible with Animation */}
            <Animated.View style={languageAnimatedStyle}>
              <View className="pl-6 py-2">
                <LanguageSettings />
              </View>
            </Animated.View>
          </Animated.View>

          {/* App Settings */}
          <Animated.View className="px-6 mb-6">
            <Text className="text-lg font-bold mb-2 text-foreground dark:text-neutral-100">{t('settings.about')}</Text>
            
            <TouchableOpacity 
              className="flex-row items-center py-4 border-b border-border dark:border-neutral-700"
              onPress={() => {
                Alert.alert(
                  t('settings.resetOnboardingTitle'),
                  t('settings.resetOnboardingMessage'),
                  [
                    { text: t('common.cancel'), style: 'cancel' },
                    { 
                      text: t('common.ok'), 
                      onPress: async () => {
                        try {
                          await resetToShowOnboarding();
                          Alert.alert(
                            t('settings.resetOnboardingSuccess'), 
                            t('settings.resetOnboardingSuccessMessage')
                          );
                        } catch (error) {
                          console.error('Error resetting onboarding:', error);
                          Alert.alert(
                            t('common.error'), 
                            'Failed to reset onboarding. Please try again.'
                          );
                        }
                      }
                    }
                  ]
                );
              }}
            >
              <Ionicons name="help-circle-outline" size={24} color={theme.colors.text} />
              <View className="flex-1 ml-3">
                <Text className="text-base text-foreground dark:text-neutral-100">
                  {t('settings.showOnboarding')}
                </Text>
                <Text className="text-sm text-muted-foreground dark:text-neutral-400">
                  {t('settings.showOnboardingDescription')}
                </Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color={theme.colors.text} />
            </TouchableOpacity>
          </Animated.View>
           {/* App Info Section */}
           <Animated.View className="px-6 mb-8">
            <Text className="text-lg font-bold mb-2 text-foreground dark:text-neutral-100">{t('settings.about')}</Text>
            
            <TouchableOpacity 
              className="flex-row items-center py-4 border-b border-border dark:border-neutral-700"
              // onPress={() => router.push('/(protected)/about')}
            >
              <Ionicons name="information-circle-outline" size={24} color={theme.colors.text} />
              <Text className="ml-3 text-base text-foreground dark:text-neutral-100">{t('settings.aboutApp')}</Text>
              <Ionicons name="chevron-forward" size={20} color={theme.colors.text} style={{marginLeft: 'auto'}} />
            </TouchableOpacity>
            
            <TouchableOpacity 
              className="flex-row items-center py-4 border-b border-border dark:border-neutral-700"
              // onPress={() => router.push('/(protected)/privacy')}
            >
              <Ionicons name="shield-outline" size={24} color={theme.colors.text} />
              <Text className="ml-3 text-base text-foreground dark:text-neutral-100">{t('settings.privacyPolicy')}</Text>
              <Ionicons name="chevron-forward" size={20} color={theme.colors.text} style={{marginLeft: 'auto'}} />
            </TouchableOpacity>
            
            <TouchableOpacity 
              className="flex-row items-center py-4 border-b border-border dark:border-neutral-700"
              // onPress={() => router.push('/(protected)/terms')}
            >
              <Ionicons name="document-text-outline" size={24} color={theme.colors.text} />
              <Text className="ml-3 text-base text-foreground dark:text-neutral-100">{t('settings.termsOfService')}</Text>
              <Ionicons name="chevron-forward" size={20} color={theme.colors.text} style={{marginLeft: 'auto'}} />
            </TouchableOpacity>
          </Animated.View>
          
          {/* Logout and Delete Section */}
          <Animated.View className="px-6 mb-6">
            <Text className="text-lg font-bold mb-2 text-foreground dark:text-neutral-100">{t('settings.session')}</Text>
            
            <TouchableOpacity 
              className="flex-row items-center py-4 border-b border-border dark:border-neutral-700"
              onPress={handleSignOut}
              disabled={isLoading}
            >
              <Ionicons name="log-out-outline" size={24} color={theme.colors.text} />
              <Text className="ml-3 text-base text-foreground dark:text-neutral-100">{isLoading ? t('common.loading') : t('settings.logout')}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              className="flex-row items-center py-4 border-b border-border dark:border-neutral-700"
              onPress={confirmDeleteAccount}
              disabled={isLoading}
            >
              <Ionicons name="trash-outline" size={24} color={theme.colors.notification} />
              <Text className="ml-3 text-base text-destructive dark:text-red-400">
                {isLoading ? t('settings.processing') : t('settings.deleteAccount')}
              </Text>
            </TouchableOpacity>
          </Animated.View>
          
         
          
          <View className="items-center pb-6">
            <Text className="text-muted-foreground dark:text-neutral-500 text-sm">{t('settings.version')}</Text>
          </View>
        </Animated.ScrollView>
      
      </>
  );
}