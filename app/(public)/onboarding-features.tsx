import React, { useState } from 'react';
import { View, Text } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Button } from '@/components/ui/Button';
import { useTranslation } from '@/hooks/useTranslation';
import { useOnboardingStore } from '@/store/onboardingStore';

interface FeatureSlide {
  id: string;
  icon: string;
  titleKey: string;
  descriptionKey: string;
  aiFeatureKey: string;
}

const features: FeatureSlide[] = [
  {
    id: '1',
    icon: '🧠',
    titleKey: 'onboarding.aiLabResultsTitle',
    descriptionKey: 'onboarding.aiLabResultsDescription',
    aiFeatureKey: 'onboarding.aiLabResultsFeature'
  },
  {
    id: '2',
    icon: '💊',
    titleKey: 'onboarding.smartMedicationTitle',
    descriptionKey: 'onboarding.smartMedicationDescription',
    aiFeatureKey: 'onboarding.smartMedicationFeature'
  },
  {
    id: '3',
    icon: '📱',
    titleKey: 'onboarding.aiHealthcareChatTitle',
    descriptionKey: 'onboarding.aiHealthcareChatDescription',
    aiFeatureKey: 'onboarding.aiHealthcareChatFeature'
  },
  {
    id: '4',
    icon: '📊',
    titleKey: 'onboarding.smartHealthTrackingTitle',
    descriptionKey: 'onboarding.smartHealthTrackingDescription',
    aiFeatureKey: 'onboarding.smartHealthTrackingFeature'
  }
];

export default function OnboardingFeatures() {
  const { t } = useTranslation();
  const { hideFutureOnboarding, skipForSession } = useOnboardingStore();
  const [currentSlide, setCurrentSlide] = useState(0);

  const handleNext = () => {
    if (currentSlide < features.length - 1) {
      setCurrentSlide(currentSlide + 1);
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentSlide > 0) {
      setCurrentSlide(currentSlide - 1);
    }
  };

  const handleComplete = async () => {
    await hideFutureOnboarding();
    router.replace('/(public)/sign-in');
  };

  const handleSkip = () => {
    skipForSession();
    router.replace('/(public)/sign-in');
  };

  const handleDontShowAgain = handleComplete;

  const currentFeature = features[currentSlide];

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900">
      <View className="flex-1 px-6 pt-4">
        {/* Header */}
        <View className="flex-row justify-between items-center mb-6">
          <Text className="text-sm text-muted-foreground dark:text-neutral-400">
            {currentSlide + 1} of {features.length}
          </Text>
          <View className="flex-row">
            <Button
              title={t('onboarding.skip')}
              onPress={handleSkip}
              variant="outline"
            />
            <View className="w-2" />
            <Button
              title={t('onboarding.dontShowAgain')}
              onPress={handleDontShowAgain}
              variant="outline"
            />
          </View>
        </View>

        {/* Progress Bar */}
        <View className="flex-row mb-8">
          {features.map((_, index) => (
            <React.Fragment key={index}>
              <View
                className={`flex-1 h-2 rounded-full ${
                  index <= currentSlide 
                    ? 'bg-primary' 
                    : 'bg-muted dark:bg-neutral-700'
                }`}
              />
              {index < features.length - 1 && <View className="w-2" />}
            </React.Fragment>
          ))}
        </View>

        {/* Feature Content */}
        <View className="flex-1 justify-center">
          <View className="items-center">
            <View className="w-32 h-32 bg-card dark:bg-neutral-800 rounded-3xl items-center justify-center mb-8 shadow-lg border border-border dark:border-neutral-700">
              <View className="w-20 h-20 bg-primary/10 rounded-2xl items-center justify-center">
                <Text className="text-4xl">{currentFeature.icon}</Text>
              </View>
            </View>
            
            <Text className="text-2xl font-bold text-center text-foreground dark:text-neutral-100 mb-4">
              {t(currentFeature.titleKey)}
            </Text>
            
            <Text className="text-base text-center text-muted-foreground dark:text-neutral-400 leading-relaxed mb-8 max-w-sm">
              {t(currentFeature.descriptionKey)}
            </Text>
            
            {/* AI Feature Highlight */}
            <View className="bg-card dark:bg-neutral-800 p-6 rounded-2xl border border-border dark:border-neutral-700 shadow-sm max-w-sm w-full">
              <View className="flex-row items-center mb-3">
                <View className="w-8 h-8 bg-primary/10 rounded-lg items-center justify-center mr-3">
                  <Text className="text-sm">✨</Text>
                </View>
                <Text className="text-sm font-semibold text-primary dark:text-primary">
                  {t('onboarding.aiPowered')}
                </Text>
              </View>
              <Text className="text-sm text-foreground dark:text-neutral-200 leading-relaxed">
                {t(currentFeature.aiFeatureKey)}
              </Text>
            </View>
          </View>
        </View>

        {/* Navigation Buttons */}
        <View className="pb-8">
          {currentSlide > 0 ? (
            <View className="flex-row">
              <View className="flex-1">
                <Button
                  title={t('onboarding.previous')}
                  onPress={handlePrevious}
                  variant="outline"
                  fullWidth
                />
              </View>
              <View className="w-3" />
              <View className="flex-1">
                <Button
                  title={currentSlide === features.length - 1 ? t('onboarding.getStarted') : t('onboarding.next')}
                  onPress={handleNext}
                  variant="primary"
                  fullWidth
                />
              </View>
            </View>
          ) : (
            <Button
              title={t('onboarding.next')}
              onPress={handleNext}
              variant="primary"
              fullWidth
            />
          )}
        </View>
      </View>
    </SafeAreaView>
  );
}