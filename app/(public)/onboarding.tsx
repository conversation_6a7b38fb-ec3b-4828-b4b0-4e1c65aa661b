import React from 'react';
import { View, Text } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Button } from '@/components/ui/Button';
import { useTranslation } from '@/hooks/useTranslation';
import { useOnboardingStore } from '@/store/onboardingStore';

export default function OnboardingWelcome() {
  const { t } = useTranslation();
  const { hideFutureOnboarding, skipForSession } = useOnboardingStore();

  const handleGetStarted = () => {
    router.push('/(public)/onboarding-features');
  };

  const handleSkip = async () => {
    skipForSession();
    router.replace('/(public)/sign-in');
  };

  const handleDontShowAgain = async () => {
    await hideFutureOnboarding();
    router.replace('/(public)/sign-in');
  };

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900">
      
        <View className="px-6 pt-6 pb-8">
          {/* Header */}
          <View className="items-center mb-6">
            <View className="w-20 h-20 bg-card dark:bg-neutral-800 rounded-3xl items-center justify-center mb-4 shadow-lg border border-border dark:border-neutral-700">
              <View className="w-14 h-14 bg-primary/10 rounded-2xl items-center justify-center">
                <Text className="text-2xl">🏥</Text>
              </View>
            </View>
            <Text className="text-2xl font-bold text-center text-foreground dark:text-neutral-100 mb-2">
              {t('onboarding.welcomeToRecepturko')}
            </Text>
            <Text className="text-sm text-center text-muted-foreground dark:text-neutral-400 leading-relaxed max-w-xs">
              {t('onboarding.welcomeSubtitle')}
            </Text>
          </View>

          {/* Feature Cards */}
          <View className="mb-6">
            <View className="bg-card dark:bg-neutral-800 p-4 rounded-2xl border border-border dark:border-neutral-700 shadow-sm">
              <View className="flex-row items-center mb-2">
                <View className="w-10 h-10 bg-primary/10 rounded-xl items-center justify-center mr-3">
                  <Text className="text-lg">🧠</Text>
                </View>
                <Text className="text-base font-semibold text-foreground dark:text-neutral-100 flex-1">
                  {t('onboarding.aiAnalysis')}
                </Text>
              </View>
              <Text className="text-xs text-muted-foreground dark:text-neutral-400 leading-relaxed">
                {t('onboarding.aiLabResultsDescription')}
              </Text>
            </View>
            <View className="h-3" />

            <View className="bg-card dark:bg-neutral-800 p-4 rounded-2xl border border-border dark:border-neutral-700 shadow-sm">
              <View className="flex-row items-center mb-2">
                <View className="w-10 h-10 bg-primary/10 rounded-xl items-center justify-center mr-3">
                  <Text className="text-lg">📊</Text>
                </View>
                <Text className="text-base font-semibold text-foreground dark:text-neutral-100 flex-1">
                  {t('onboarding.labResults')}
                </Text>
              </View>
              <Text className="text-xs text-muted-foreground dark:text-neutral-400 leading-relaxed">
                {t('onboarding.smartMedicationDescription')}
              </Text>
            </View>
            <View className="h-3" />

            <View className="bg-card dark:bg-neutral-800 p-4 rounded-2xl border border-border dark:border-neutral-700 shadow-sm">
              <View className="flex-row items-center mb-2">
                <View className="w-10 h-10 bg-primary/10 rounded-xl items-center justify-center mr-3">
                  <Text className="text-lg">💊</Text>
                </View>
                <Text className="text-base font-semibold text-foreground dark:text-neutral-100 flex-1">
                  {t('onboarding.medications')}
                </Text>
              </View>
              <Text className="text-xs text-muted-foreground dark:text-neutral-400 leading-relaxed">
                {t('onboarding.aiHealthcareChatDescription')}
              </Text>
            </View>
          </View>

          {/* Action Buttons */}
          <View>
            <Button
              title={t('onboarding.getStarted')}
              onPress={handleGetStarted}
              variant="primary"
              fullWidth
            />
            <View className="h-3" />
            <Button
              title={t('onboarding.skipForNow')}
              onPress={handleSkip}
              variant="outline"
              fullWidth
            />
            <View className="h-3" />
            <Button
              title={t('onboarding.dontShowAgain')}
              onPress={handleDontShowAgain}
              variant="outline"
              fullWidth
            />
          </View>
        </View>
      
    </SafeAreaView>
  );
}