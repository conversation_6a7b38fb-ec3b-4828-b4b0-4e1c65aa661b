import { Clerk<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@clerk/clerk-expo'
import { tokenCache } from '@/utils/cache'
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { Slot } from "expo-router";
import "../global.css";
import { useColorScheme } from "react-native"; // Import hook
import {DarkTheme, ThemeProvider, DefaultTheme } from "@react-navigation/native";
import { StatusBar } from "expo-status-bar";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
// Initialize i18n
import '@/i18n';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes - more reasonable for healthcare data
      gcTime: 1000 * 60 * 60 * 24, // 24 hours
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
    },
  },
});

export default function RootLayout() {
  const publishablekey = process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY;

  if (!publishablekey) {
    throw new Error('Missing environment variable: EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY')
  }

  const colorScheme = useColorScheme();


  return (
    <ClerkProvider publishableKey={publishablekey} tokenCache={tokenCache}>
      <QueryClientProvider client={queryClient}>
        <SafeAreaProvider>  
          <GestureHandlerRootView style={{ flex: 1 }}>
            <ThemeProvider value={colorScheme === "dark" ? DarkTheme : DefaultTheme}>
              <ClerkLoaded>
                <Slot />
                <StatusBar style={"auto"} />
              </ClerkLoaded>
            </ThemeProvider>
          </GestureHandlerRootView>
        </SafeAreaProvider>
      </QueryClientProvider>
    </ClerkProvider>
  );
}
