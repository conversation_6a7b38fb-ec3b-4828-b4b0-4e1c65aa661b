import { create, StateCreator } from "zustand";

interface RateLimitState {
  priceCheckTimestamps: number[];
  currentCountdown: number;
  priceCheckingMedicine: string | null; // ID of medicine being price checked
  checkRateLimit: () => { allowed: boolean; remainingTime: number };
  recordRequest: () => void;
  getRemainingRequests: () => number;
  getTimeUntilNextRequest: () => number;
  startCountdown: () => void;
  stopCountdown: () => void;
  setPriceCheckingMedicine: (medicineId: string | null) => void;
}

const RATE_LIMIT_WINDOW = 60 * 1000; // 60 seconds in milliseconds
const MAX_REQUESTS = 3;

// Global timer reference
let globalCountdownTimer: ReturnType<typeof setInterval> | null = null;

const storeCreator: StateCreator<RateLimitState> = (set, get) => ({
  priceCheckTimestamps: [],
  currentCountdown: 0,
  priceCheckingMedicine: null,
  
  checkRateLimit: () => {
    const now = Date.now();
    const { priceCheckTimestamps } = get();
    
    // Filter out timestamps older than 60 seconds
    const recentRequests = priceCheckTimestamps.filter(
      timestamp => now - timestamp < RATE_LIMIT_WINDOW
    );
    
    if (recentRequests.length >= MAX_REQUESTS) {
      // Calculate remaining time until the oldest request expires
      const oldestRequest = Math.min(...recentRequests);
      const remainingTime = Math.ceil((RATE_LIMIT_WINDOW - (now - oldestRequest)) / 1000);
      
      return { allowed: false, remainingTime };
    }
    
    return { allowed: true, remainingTime: 0 };
  },
  
  recordRequest: () => {
    const now = Date.now();
    set(state => {
      // Add current timestamp and keep only the last 3 requests
      const newTimestamps = [...state.priceCheckTimestamps, now]
        .filter(timestamp => now - timestamp < RATE_LIMIT_WINDOW)
        .slice(-MAX_REQUESTS);
      
      return { priceCheckTimestamps: newTimestamps };
    });
  },
  
  getRemainingRequests: () => {
    const now = Date.now();
    const { priceCheckTimestamps } = get();
    
    const recentRequests = priceCheckTimestamps.filter(
      timestamp => now - timestamp < RATE_LIMIT_WINDOW
    );
    
    return Math.max(0, MAX_REQUESTS - recentRequests.length);
  },
  
  getTimeUntilNextRequest: () => {
    const { checkRateLimit } = get();
    const result = checkRateLimit();
    
    return result.allowed ? 0 : result.remainingTime;
  },

  startCountdown: () => {
    const { checkRateLimit } = get();
    const result = checkRateLimit();
    
    if (!result.allowed && result.remainingTime > 0) {
      // Clear any existing timer
      if (globalCountdownTimer) {
        clearInterval(globalCountdownTimer);
      }
      
      // Set initial countdown
      set({ currentCountdown: result.remainingTime });
      
      // Start global timer
      globalCountdownTimer = setInterval(() => {
        const { currentCountdown } = get();
        if (currentCountdown <= 1) {
          // Stop countdown
          clearInterval(globalCountdownTimer!);
          globalCountdownTimer = null;
          set({ currentCountdown: 0 });
        } else {
          set({ currentCountdown: currentCountdown - 1 });
        }
      }, 1000);
    }
  },

  stopCountdown: () => {
    if (globalCountdownTimer) {
      clearInterval(globalCountdownTimer);
      globalCountdownTimer = null;
    }
    set({ currentCountdown: 0 });
  },

  setPriceCheckingMedicine: (medicineId: string | null) => {
    set({ priceCheckingMedicine: medicineId });
  }
});

export const useRateLimitStore = create<RateLimitState>(storeCreator);