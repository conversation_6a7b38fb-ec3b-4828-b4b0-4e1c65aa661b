import { create, StateCreator } from "zustand";
import * as SecureStore from 'expo-secure-store';

const SHOULD_SHOW_ONBOARDING_KEY = 'should-show-onboarding';

interface OnboardingState {
  shouldShowOnboarding: boolean | null;
  hideFutureOnboarding: () => Promise<void>;
  resetToShowOnboarding: () => Promise<void>;
  skipForSession: () => void;
}

const storeCreator: StateCreator<OnboardingState> = (set) => ({
  shouldShowOnboarding: null, // null = not loaded yet
  
  hideFutureOnboarding: async () => {
    try {
      await SecureStore.setItemAsync(SHOULD_SHOW_ONBOARDING_KEY, 'false');
      set({ shouldShowOnboarding: false });
    } catch (error) {
      console.error('Error hiding future onboarding:', error);
    }
  },
  
  resetToShowOnboarding: async () => {
    try {
      await SecureStore.deleteItemAsync(SHOULD_SHOW_ONBOARDING_KEY);
      set({ shouldShowOnboarding: true });
    } catch (error) {
      console.error('Error resetting onboarding to show:', error);
    }
  },
  
  skipForSession: () => {
    // Temporarily hide onboarding for this session only
    set({ shouldShowOnboarding: false });
  },
});

export const useOnboardingStore = create<OnboardingState>(storeCreator);

// Initialize the store in a controlled manner
const initializeOnboardingStore = async () => {
  try {
    const value = await SecureStore.getItemAsync(SHOULD_SHOW_ONBOARDING_KEY);
    // If user explicitly set "don't show again", respect that (false)
    // Otherwise, show onboarding (true) - this covers new users and users who skipped
    useOnboardingStore.setState({ shouldShowOnboarding: value !== 'false' });
  } catch (error) {
    console.error('Error checking onboarding preference:', error);
    useOnboardingStore.setState({ shouldShowOnboarding: true });
  }
};

// Call initialization when needed
initializeOnboardingStore();