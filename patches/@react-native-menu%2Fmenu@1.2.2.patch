diff --git a/node_modules/@react-native-menu/menu/android/build/generated/source/buildConfig/debug/com/reactnativemenu/BuildConfig.java b/android/build/generated/source/buildConfig/debug/com/reactnativemenu/BuildConfig.java
new file mode 100644
index 0000000000000000000000000000000000000000..b642426dc453508d1bf32a1df895c770887a6650
--- /dev/null
+++ b/android/build/generated/source/buildConfig/debug/com/reactnativemenu/BuildConfig.java
@@ -0,0 +1,12 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package com.reactnativemenu;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "com.reactnativemenu";
+  public static final String BUILD_TYPE = "debug";
+  // Field from default config.
+  public static final boolean IS_NEW_ARCHITECTURE_ENABLED = true;
+}
diff --git a/node_modules/@react-native-menu/menu/android/build/generated/source/codegen/java/com/facebook/react/viewmanagers/MenuViewManagerDelegate.java b/android/build/generated/source/codegen/java/com/facebook/react/viewmanagers/MenuViewManagerDelegate.java
new file mode 100644
index 0000000000000000000000000000000000000000..97d3777de95312ecd3a7b761c62d9b404f2b7c58
--- /dev/null
+++ b/android/build/generated/source/codegen/java/com/facebook/react/viewmanagers/MenuViewManagerDelegate.java
@@ -0,0 +1,49 @@
+/**
+* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+*
+* Do not edit this file as changes may cause incorrect behavior and will be lost
+* once the code is regenerated.
+*
+* @generated by codegen project: GeneratePropsJavaDelegate.js
+*/
+
+package com.facebook.react.viewmanagers;
+
+import android.view.View;
+import androidx.annotation.Nullable;
+import com.facebook.react.bridge.ReadableArray;
+import com.facebook.react.bridge.ReadableMap;
+import com.facebook.react.uimanager.BaseViewManager;
+import com.facebook.react.uimanager.BaseViewManagerDelegate;
+import com.facebook.react.uimanager.LayoutShadowNode;
+
+public class MenuViewManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & MenuViewManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
+  public MenuViewManagerDelegate(U viewManager) {
+    super(viewManager);
+  }
+  @Override
+  public void setProperty(T view, String propName, @Nullable Object value) {
+    switch (propName) {
+      case "actions":
+        mViewManager.setActions(view, (ReadableArray) value);
+        break;
+      case "actionsHash":
+        mViewManager.setActionsHash(view, value == null ? null : (String) value);
+        break;
+      case "title":
+        mViewManager.setTitle(view, value == null ? null : (String) value);
+        break;
+      case "themeVariant":
+        mViewManager.setThemeVariant(view, value == null ? null : (String) value);
+        break;
+      case "shouldOpenOnLongPress":
+        mViewManager.setShouldOpenOnLongPress(view, value == null ? false : (boolean) value);
+        break;
+      case "hitSlop":
+        mViewManager.setHitSlop(view, (ReadableMap) value);
+        break;
+      default:
+        super.setProperty(view, propName, value);
+    }
+  }
+}
diff --git a/node_modules/@react-native-menu/menu/android/build/generated/source/codegen/java/com/facebook/react/viewmanagers/MenuViewManagerInterface.java b/android/build/generated/source/codegen/java/com/facebook/react/viewmanagers/MenuViewManagerInterface.java
new file mode 100644
index 0000000000000000000000000000000000000000..7ef810f074c6ffb343731351c8d11525e63c23d9
--- /dev/null
+++ b/android/build/generated/source/codegen/java/com/facebook/react/viewmanagers/MenuViewManagerInterface.java
@@ -0,0 +1,25 @@
+/**
+* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+*
+* Do not edit this file as changes may cause incorrect behavior and will be lost
+* once the code is regenerated.
+*
+* @generated by codegen project: GeneratePropsJavaInterface.js
+*/
+
+package com.facebook.react.viewmanagers;
+
+import android.view.View;
+import androidx.annotation.Nullable;
+import com.facebook.react.bridge.ReadableArray;
+import com.facebook.react.bridge.ReadableMap;
+import com.facebook.react.uimanager.ViewManagerWithGeneratedInterface;
+
+public interface MenuViewManagerInterface<T extends View> extends ViewManagerWithGeneratedInterface {
+  void setActions(T view, @Nullable ReadableArray value);
+  void setActionsHash(T view, @Nullable String value);
+  void setTitle(T view, @Nullable String value);
+  void setThemeVariant(T view, @Nullable String value);
+  void setShouldOpenOnLongPress(T view, boolean value);
+  void setHitSlop(T view, @Nullable ReadableMap value);
+}
diff --git a/node_modules/@react-native-menu/menu/android/build/generated/source/codegen/jni/CMakeLists.txt b/android/build/generated/source/codegen/jni/CMakeLists.txt
new file mode 100644
index 0000000000000000000000000000000000000000..c098f8e89635403bed2318fd5d3b090e280bd343
--- /dev/null
+++ b/android/build/generated/source/codegen/jni/CMakeLists.txt
@@ -0,0 +1,36 @@
+# Copyright (c) Meta Platforms, Inc. and affiliates.
+#
+# This source code is licensed under the MIT license found in the
+# LICENSE file in the root directory of this source tree.
+
+cmake_minimum_required(VERSION 3.13)
+set(CMAKE_VERBOSE_MAKEFILE on)
+
+file(GLOB react_codegen_SRCS CONFIGURE_DEPENDS *.cpp react/renderer/components/RNMenuViewSpec/*.cpp)
+
+add_library(
+  react_codegen_RNMenuViewSpec
+  OBJECT
+  ${react_codegen_SRCS}
+)
+
+target_include_directories(react_codegen_RNMenuViewSpec PUBLIC . react/renderer/components/RNMenuViewSpec)
+
+target_link_libraries(
+  react_codegen_RNMenuViewSpec
+  fbjni
+  jsi
+  # We need to link different libraries based on whether we are building rncore or not, that's necessary
+  # because we want to break a circular dependency between react_codegen_rncore and reactnative
+  reactnative
+)
+
+target_compile_options(
+  react_codegen_RNMenuViewSpec
+  PRIVATE
+  -DLOG_TAG=\"ReactNative\"
+  -fexceptions
+  -frtti
+  -std=c++20
+  -Wall
+)
diff --git a/node_modules/@react-native-menu/menu/android/build/generated/source/codegen/jni/RNMenuViewSpec-generated.cpp b/android/build/generated/source/codegen/jni/RNMenuViewSpec-generated.cpp
new file mode 100644
index 0000000000000000000000000000000000000000..a89e02851b05ef70239c80d3d3a901158d48bdef
--- /dev/null
+++ b/android/build/generated/source/codegen/jni/RNMenuViewSpec-generated.cpp
@@ -0,0 +1,22 @@
+
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GenerateModuleJniCpp.js
+ */
+
+#include "RNMenuViewSpec.h"
+
+namespace facebook::react {
+
+
+
+std::shared_ptr<TurboModule> RNMenuViewSpec_ModuleProvider(const std::string &moduleName, const JavaTurboModule::InitParams &params) {
+
+  return nullptr;
+}
+
+} // namespace facebook::react
diff --git a/node_modules/@react-native-menu/menu/android/build/generated/source/codegen/jni/RNMenuViewSpec.h b/android/build/generated/source/codegen/jni/RNMenuViewSpec.h
new file mode 100644
index 0000000000000000000000000000000000000000..e3293d19c1520e19a1e573383a7439db5f3c06bb
--- /dev/null
+++ b/android/build/generated/source/codegen/jni/RNMenuViewSpec.h
@@ -0,0 +1,24 @@
+
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GenerateModuleJniH.js
+ */
+
+#pragma once
+
+#include <ReactCommon/JavaTurboModule.h>
+#include <ReactCommon/TurboModule.h>
+#include <jsi/jsi.h>
+
+namespace facebook::react {
+
+
+
+JSI_EXPORT
+std::shared_ptr<TurboModule> RNMenuViewSpec_ModuleProvider(const std::string &moduleName, const JavaTurboModule::InitParams &params);
+
+} // namespace facebook::react
diff --git a/node_modules/@react-native-menu/menu/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/ComponentDescriptors.cpp b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/ComponentDescriptors.cpp
new file mode 100644
index 0000000000000000000000000000000000000000..556810a3135f4c14e58b8d167793dc1db6eea71a
--- /dev/null
+++ b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/ComponentDescriptors.cpp
@@ -0,0 +1,22 @@
+
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GenerateComponentDescriptorCpp.js
+ */
+
+#include <react/renderer/components/RNMenuViewSpec/ComponentDescriptors.h>
+#include <react/renderer/core/ConcreteComponentDescriptor.h>
+#include <react/renderer/componentregistry/ComponentDescriptorProviderRegistry.h>
+
+namespace facebook::react {
+
+void RNMenuViewSpec_registerComponentDescriptorsFromCodegen(
+  std::shared_ptr<const ComponentDescriptorProviderRegistry> registry) {
+registry->add(concreteComponentDescriptorProvider<MenuViewComponentDescriptor>());
+}
+
+} // namespace facebook::react
diff --git a/node_modules/@react-native-menu/menu/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/ComponentDescriptors.h b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/ComponentDescriptors.h
new file mode 100644
index 0000000000000000000000000000000000000000..a3d5a14b5cd0c4c11d1a13d2849e8f92227d346a
--- /dev/null
+++ b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/ComponentDescriptors.h
@@ -0,0 +1,24 @@
+
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GenerateComponentDescriptorH.js
+ */
+
+#pragma once
+
+#include <react/renderer/components/RNMenuViewSpec/ShadowNodes.h>
+#include <react/renderer/core/ConcreteComponentDescriptor.h>
+#include <react/renderer/componentregistry/ComponentDescriptorProviderRegistry.h>
+
+namespace facebook::react {
+
+using MenuViewComponentDescriptor = ConcreteComponentDescriptor<MenuViewShadowNode>;
+
+void RNMenuViewSpec_registerComponentDescriptorsFromCodegen(
+  std::shared_ptr<const ComponentDescriptorProviderRegistry> registry);
+
+} // namespace facebook::react
diff --git a/node_modules/@react-native-menu/menu/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/EventEmitters.cpp b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/EventEmitters.cpp
new file mode 100644
index 0000000000000000000000000000000000000000..733d6bf75805ba666efc36f6436a9dd4d456ae96
--- /dev/null
+++ b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/EventEmitters.cpp
@@ -0,0 +1,42 @@
+
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GenerateEventEmitterCpp.js
+ */
+
+#include <react/renderer/components/RNMenuViewSpec/EventEmitters.h>
+
+
+namespace facebook::react {
+
+void MenuViewEventEmitter::onPressAction(OnPressAction $event) const {
+  dispatchEvent("pressAction", [$event=std::move($event)](jsi::Runtime &runtime) {
+    auto $payload = jsi::Object(runtime);
+    $payload.setProperty(runtime, "event", $event.event);
+    return $payload;
+  });
+}
+
+
+void MenuViewEventEmitter::onCloseMenu(OnCloseMenu $event) const {
+  dispatchEvent("closeMenu", [$event=std::move($event)](jsi::Runtime &runtime) {
+    auto $payload = jsi::Object(runtime);
+    $payload.setProperty(runtime, "event", $event.event);
+    return $payload;
+  });
+}
+
+
+void MenuViewEventEmitter::onOpenMenu(OnOpenMenu $event) const {
+  dispatchEvent("openMenu", [$event=std::move($event)](jsi::Runtime &runtime) {
+    auto $payload = jsi::Object(runtime);
+    $payload.setProperty(runtime, "event", $event.event);
+    return $payload;
+  });
+}
+
+} // namespace facebook::react
diff --git a/node_modules/@react-native-menu/menu/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/EventEmitters.h b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/EventEmitters.h
new file mode 100644
index 0000000000000000000000000000000000000000..2e68763d55d1af08402b0792481155753a8d7be4
--- /dev/null
+++ b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/EventEmitters.h
@@ -0,0 +1,37 @@
+
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GenerateEventEmitterH.js
+ */
+#pragma once
+
+#include <react/renderer/components/view/ViewEventEmitter.h>
+
+
+namespace facebook::react {
+class MenuViewEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnPressAction {
+      std::string event;
+    };
+
+  struct OnCloseMenu {
+      std::string event;
+    };
+
+  struct OnOpenMenu {
+      std::string event;
+    };
+  void onPressAction(OnPressAction value) const;
+
+  void onCloseMenu(OnCloseMenu value) const;
+
+  void onOpenMenu(OnOpenMenu value) const;
+};
+} // namespace facebook::react
diff --git a/node_modules/@react-native-menu/menu/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/Props.cpp b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/Props.cpp
new file mode 100644
index 0000000000000000000000000000000000000000..b397ad02d9247cfd66dd2c8551923de4c3f457f6
--- /dev/null
+++ b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/Props.cpp
@@ -0,0 +1,30 @@
+
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GeneratePropsCpp.js
+ */
+
+#include <react/renderer/components/RNMenuViewSpec/Props.h>
+#include <react/renderer/core/PropsParserContext.h>
+#include <react/renderer/core/propsConversions.h>
+
+namespace facebook::react {
+
+MenuViewProps::MenuViewProps(
+    const PropsParserContext &context,
+    const MenuViewProps &sourceProps,
+    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),
+
+    actions(convertRawProp(context, rawProps, "actions", sourceProps.actions, {})),
+    actionsHash(convertRawProp(context, rawProps, "actionsHash", sourceProps.actionsHash, {})),
+    title(convertRawProp(context, rawProps, "title", sourceProps.title, {})),
+    themeVariant(convertRawProp(context, rawProps, "themeVariant", sourceProps.themeVariant, {})),
+    shouldOpenOnLongPress(convertRawProp(context, rawProps, "shouldOpenOnLongPress", sourceProps.shouldOpenOnLongPress, {false})),
+    hitSlop(convertRawProp(context, rawProps, "hitSlop", sourceProps.hitSlop, {}))
+      {}
+
+} // namespace facebook::react
diff --git a/node_modules/@react-native-menu/menu/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/Props.h b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/Props.h
new file mode 100644
index 0000000000000000000000000000000000000000..5bc14f4f6f1dbe413c06ddb5a2f24bdaf1885517
--- /dev/null
+++ b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/Props.h
@@ -0,0 +1,258 @@
+
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GeneratePropsH.js
+ */
+#pragma once
+
+#include <react/renderer/components/view/ViewProps.h>
+#include <react/renderer/core/PropsParserContext.h>
+#include <react/renderer/core/propsConversions.h>
+#include <vector>
+
+namespace facebook::react {
+
+struct MenuViewActionsAttributesStruct {
+  bool destructive{false};
+  bool disabled{false};
+  bool hidden{false};
+};
+
+static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, MenuViewActionsAttributesStruct &result) {
+  auto map = (std::unordered_map<std::string, RawValue>)value;
+
+  auto tmp_destructive = map.find("destructive");
+  if (tmp_destructive != map.end()) {
+    fromRawValue(context, tmp_destructive->second, result.destructive);
+  }
+  auto tmp_disabled = map.find("disabled");
+  if (tmp_disabled != map.end()) {
+    fromRawValue(context, tmp_disabled->second, result.disabled);
+  }
+  auto tmp_hidden = map.find("hidden");
+  if (tmp_hidden != map.end()) {
+    fromRawValue(context, tmp_hidden->second, result.hidden);
+  }
+}
+
+static inline std::string toString(const MenuViewActionsAttributesStruct &value) {
+  return "[Object MenuViewActionsAttributesStruct]";
+}
+
+struct MenuViewActionsSubactionsAttributesStruct {
+  bool destructive{false};
+  bool disabled{false};
+  bool hidden{false};
+};
+
+static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, MenuViewActionsSubactionsAttributesStruct &result) {
+  auto map = (std::unordered_map<std::string, RawValue>)value;
+
+  auto tmp_destructive = map.find("destructive");
+  if (tmp_destructive != map.end()) {
+    fromRawValue(context, tmp_destructive->second, result.destructive);
+  }
+  auto tmp_disabled = map.find("disabled");
+  if (tmp_disabled != map.end()) {
+    fromRawValue(context, tmp_disabled->second, result.disabled);
+  }
+  auto tmp_hidden = map.find("hidden");
+  if (tmp_hidden != map.end()) {
+    fromRawValue(context, tmp_hidden->second, result.hidden);
+  }
+}
+
+static inline std::string toString(const MenuViewActionsSubactionsAttributesStruct &value) {
+  return "[Object MenuViewActionsSubactionsAttributesStruct]";
+}
+
+struct MenuViewActionsSubactionsStruct {
+  std::string id{};
+  std::string title{};
+  int titleColor{0};
+  std::string subtitle{};
+  std::string state{};
+  std::string image{};
+  int imageColor{0};
+  bool displayInline{false};
+  MenuViewActionsSubactionsAttributesStruct attributes{};
+};
+
+static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, MenuViewActionsSubactionsStruct &result) {
+  auto map = (std::unordered_map<std::string, RawValue>)value;
+
+  auto tmp_id = map.find("id");
+  if (tmp_id != map.end()) {
+    fromRawValue(context, tmp_id->second, result.id);
+  }
+  auto tmp_title = map.find("title");
+  if (tmp_title != map.end()) {
+    fromRawValue(context, tmp_title->second, result.title);
+  }
+  auto tmp_titleColor = map.find("titleColor");
+  if (tmp_titleColor != map.end()) {
+    fromRawValue(context, tmp_titleColor->second, result.titleColor);
+  }
+  auto tmp_subtitle = map.find("subtitle");
+  if (tmp_subtitle != map.end()) {
+    fromRawValue(context, tmp_subtitle->second, result.subtitle);
+  }
+  auto tmp_state = map.find("state");
+  if (tmp_state != map.end()) {
+    fromRawValue(context, tmp_state->second, result.state);
+  }
+  auto tmp_image = map.find("image");
+  if (tmp_image != map.end()) {
+    fromRawValue(context, tmp_image->second, result.image);
+  }
+  auto tmp_imageColor = map.find("imageColor");
+  if (tmp_imageColor != map.end()) {
+    fromRawValue(context, tmp_imageColor->second, result.imageColor);
+  }
+  auto tmp_displayInline = map.find("displayInline");
+  if (tmp_displayInline != map.end()) {
+    fromRawValue(context, tmp_displayInline->second, result.displayInline);
+  }
+  auto tmp_attributes = map.find("attributes");
+  if (tmp_attributes != map.end()) {
+    fromRawValue(context, tmp_attributes->second, result.attributes);
+  }
+}
+
+static inline std::string toString(const MenuViewActionsSubactionsStruct &value) {
+  return "[Object MenuViewActionsSubactionsStruct]";
+}
+
+static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, std::vector<MenuViewActionsSubactionsStruct> &result) {
+  auto items = (std::vector<RawValue>)value;
+  for (const auto &item : items) {
+    MenuViewActionsSubactionsStruct newItem;
+    fromRawValue(context, item, newItem);
+    result.emplace_back(newItem);
+  }
+}
+
+
+struct MenuViewActionsStruct {
+  std::string id{};
+  std::string title{};
+  int titleColor{0};
+  std::string subtitle{};
+  std::string state{};
+  std::string image{};
+  int imageColor{0};
+  bool displayInline{false};
+  MenuViewActionsAttributesStruct attributes{};
+  std::vector<MenuViewActionsSubactionsStruct> subactions{};
+};
+
+static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, MenuViewActionsStruct &result) {
+  auto map = (std::unordered_map<std::string, RawValue>)value;
+
+  auto tmp_id = map.find("id");
+  if (tmp_id != map.end()) {
+    fromRawValue(context, tmp_id->second, result.id);
+  }
+  auto tmp_title = map.find("title");
+  if (tmp_title != map.end()) {
+    fromRawValue(context, tmp_title->second, result.title);
+  }
+  auto tmp_titleColor = map.find("titleColor");
+  if (tmp_titleColor != map.end()) {
+    fromRawValue(context, tmp_titleColor->second, result.titleColor);
+  }
+  auto tmp_subtitle = map.find("subtitle");
+  if (tmp_subtitle != map.end()) {
+    fromRawValue(context, tmp_subtitle->second, result.subtitle);
+  }
+  auto tmp_state = map.find("state");
+  if (tmp_state != map.end()) {
+    fromRawValue(context, tmp_state->second, result.state);
+  }
+  auto tmp_image = map.find("image");
+  if (tmp_image != map.end()) {
+    fromRawValue(context, tmp_image->second, result.image);
+  }
+  auto tmp_imageColor = map.find("imageColor");
+  if (tmp_imageColor != map.end()) {
+    fromRawValue(context, tmp_imageColor->second, result.imageColor);
+  }
+  auto tmp_displayInline = map.find("displayInline");
+  if (tmp_displayInline != map.end()) {
+    fromRawValue(context, tmp_displayInline->second, result.displayInline);
+  }
+  auto tmp_attributes = map.find("attributes");
+  if (tmp_attributes != map.end()) {
+    fromRawValue(context, tmp_attributes->second, result.attributes);
+  }
+  auto tmp_subactions = map.find("subactions");
+  if (tmp_subactions != map.end()) {
+    fromRawValue(context, tmp_subactions->second, result.subactions);
+  }
+}
+
+static inline std::string toString(const MenuViewActionsStruct &value) {
+  return "[Object MenuViewActionsStruct]";
+}
+
+static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, std::vector<MenuViewActionsStruct> &result) {
+  auto items = (std::vector<RawValue>)value;
+  for (const auto &item : items) {
+    MenuViewActionsStruct newItem;
+    fromRawValue(context, item, newItem);
+    result.emplace_back(newItem);
+  }
+}
+
+
+struct MenuViewHitSlopStruct {
+  int top{0};
+  int bottom{0};
+  int left{0};
+  int right{0};
+};
+
+static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, MenuViewHitSlopStruct &result) {
+  auto map = (std::unordered_map<std::string, RawValue>)value;
+
+  auto tmp_top = map.find("top");
+  if (tmp_top != map.end()) {
+    fromRawValue(context, tmp_top->second, result.top);
+  }
+  auto tmp_bottom = map.find("bottom");
+  if (tmp_bottom != map.end()) {
+    fromRawValue(context, tmp_bottom->second, result.bottom);
+  }
+  auto tmp_left = map.find("left");
+  if (tmp_left != map.end()) {
+    fromRawValue(context, tmp_left->second, result.left);
+  }
+  auto tmp_right = map.find("right");
+  if (tmp_right != map.end()) {
+    fromRawValue(context, tmp_right->second, result.right);
+  }
+}
+
+static inline std::string toString(const MenuViewHitSlopStruct &value) {
+  return "[Object MenuViewHitSlopStruct]";
+}
+class MenuViewProps final : public ViewProps {
+ public:
+  MenuViewProps() = default;
+  MenuViewProps(const PropsParserContext& context, const MenuViewProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  std::vector<MenuViewActionsStruct> actions{};
+  std::string actionsHash{};
+  std::string title{};
+  std::string themeVariant{};
+  bool shouldOpenOnLongPress{false};
+  MenuViewHitSlopStruct hitSlop{};
+};
+
+} // namespace facebook::react
diff --git a/node_modules/@react-native-menu/menu/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/RNMenuViewSpecJSI-generated.cpp b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/RNMenuViewSpecJSI-generated.cpp
new file mode 100644
index 0000000000000000000000000000000000000000..91b9fd94b71cbef7e8e1d210876ad6e7e6484f2e
--- /dev/null
+++ b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/RNMenuViewSpecJSI-generated.cpp
@@ -0,0 +1,17 @@
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GenerateModuleCpp.js
+ */
+
+#include "RNMenuViewSpecJSI.h"
+
+namespace facebook::react {
+
+
+
+
+} // namespace facebook::react
diff --git a/node_modules/@react-native-menu/menu/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/RNMenuViewSpecJSI.h b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/RNMenuViewSpecJSI.h
new file mode 100644
index 0000000000000000000000000000000000000000..ec162608dab14196709ed33a62f486beeebdce2d
--- /dev/null
+++ b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/RNMenuViewSpecJSI.h
@@ -0,0 +1,19 @@
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GenerateModuleH.js
+ */
+
+#pragma once
+
+#include <ReactCommon/TurboModule.h>
+#include <react/bridging/Bridging.h>
+
+namespace facebook::react {
+
+
+
+} // namespace facebook::react
diff --git a/node_modules/@react-native-menu/menu/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/ShadowNodes.cpp b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/ShadowNodes.cpp
new file mode 100644
index 0000000000000000000000000000000000000000..b9f7addd0027908c039304bda5212b7e9a482ea3
--- /dev/null
+++ b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/ShadowNodes.cpp
@@ -0,0 +1,17 @@
+
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GenerateShadowNodeCpp.js
+ */
+
+#include <react/renderer/components/RNMenuViewSpec/ShadowNodes.h>
+
+namespace facebook::react {
+
+extern const char MenuViewComponentName[] = "MenuView";
+
+} // namespace facebook::react
diff --git a/node_modules/@react-native-menu/menu/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/ShadowNodes.h b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/ShadowNodes.h
new file mode 100644
index 0000000000000000000000000000000000000000..d6d1ce7aab3080014dd30efa22b1914e2b952838
--- /dev/null
+++ b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/ShadowNodes.h
@@ -0,0 +1,32 @@
+
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GenerateShadowNodeH.js
+ */
+
+#pragma once
+
+#include <react/renderer/components/RNMenuViewSpec/EventEmitters.h>
+#include <react/renderer/components/RNMenuViewSpec/Props.h>
+#include <react/renderer/components/RNMenuViewSpec/States.h>
+#include <react/renderer/components/view/ConcreteViewShadowNode.h>
+#include <jsi/jsi.h>
+
+namespace facebook::react {
+
+JSI_EXPORT extern const char MenuViewComponentName[];
+
+/*
+ * `ShadowNode` for <MenuView> component.
+ */
+using MenuViewShadowNode = ConcreteViewShadowNode<
+    MenuViewComponentName,
+    MenuViewProps,
+    MenuViewEventEmitter,
+    MenuViewState>;
+
+} // namespace facebook::react
diff --git a/node_modules/@react-native-menu/menu/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/States.cpp b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/States.cpp
new file mode 100644
index 0000000000000000000000000000000000000000..b849b61af481afc94734749b1ca9322c21eecd58
--- /dev/null
+++ b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/States.cpp
@@ -0,0 +1,16 @@
+
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GenerateStateCpp.js
+ */
+#include <react/renderer/components/RNMenuViewSpec/States.h>
+
+namespace facebook::react {
+
+
+
+} // namespace facebook::react
diff --git a/node_modules/@react-native-menu/menu/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/States.h b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/States.h
new file mode 100644
index 0000000000000000000000000000000000000000..6cbe1eea4c41c02fe408a149bbf0ce9ef86d9a79
--- /dev/null
+++ b/android/build/generated/source/codegen/jni/react/renderer/components/RNMenuViewSpec/States.h
@@ -0,0 +1,29 @@
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by codegen project: GenerateStateH.js
+ */
+#pragma once
+
+#ifdef ANDROID
+#include <folly/dynamic.h>
+#endif
+
+namespace facebook::react {
+
+class MenuViewState {
+public:
+  MenuViewState() = default;
+
+#ifdef ANDROID
+  MenuViewState(MenuViewState const &previousState, folly::dynamic data){};
+  folly::dynamic getDynamic() const {
+    return {};
+  };
+#endif
+};
+
+} // namespace facebook::react
\ No newline at end of file
diff --git a/node_modules/@react-native-menu/menu/android/build/generated/source/codegen/schema.json b/android/build/generated/source/codegen/schema.json
new file mode 100644
index 0000000000000000000000000000000000000000..a33383b37003c6c0f7dfa868e1d6ed2994940996
--- /dev/null
+++ b/android/build/generated/source/codegen/schema.json
@@ -0,0 +1 @@
+{"modules":{"MenuView":{"type":"Component","components":{"MenuView":{"extendsProps":[{"type":"ReactNativeBuiltInType","knownTypeName":"ReactNativeCoreViewProps"}],"events":[{"name":"onPressAction","optional":true,"bubblingType":"direct","typeAnnotation":{"type":"EventTypeAnnotation","argument":{"type":"ObjectTypeAnnotation","properties":[{"name":"event","optional":false,"typeAnnotation":{"type":"StringTypeAnnotation"}}]}}},{"name":"onCloseMenu","optional":true,"bubblingType":"direct","typeAnnotation":{"type":"EventTypeAnnotation","argument":{"type":"ObjectTypeAnnotation","properties":[{"name":"event","optional":false,"typeAnnotation":{"type":"StringTypeAnnotation"}}]}}},{"name":"onOpenMenu","optional":true,"bubblingType":"direct","typeAnnotation":{"type":"EventTypeAnnotation","argument":{"type":"ObjectTypeAnnotation","properties":[{"name":"event","optional":false,"typeAnnotation":{"type":"StringTypeAnnotation"}}]}}}],"props":[{"name":"actions","optional":false,"typeAnnotation":{"type":"ArrayTypeAnnotation","elementType":{"type":"ObjectTypeAnnotation","properties":[{"name":"id","optional":true,"typeAnnotation":{"type":"StringTypeAnnotation","default":null}},{"name":"title","optional":false,"typeAnnotation":{"type":"StringTypeAnnotation","default":null}},{"name":"titleColor","optional":true,"typeAnnotation":{"type":"Int32TypeAnnotation","default":0}},{"name":"subtitle","optional":true,"typeAnnotation":{"type":"StringTypeAnnotation","default":null}},{"name":"state","optional":true,"typeAnnotation":{"type":"StringTypeAnnotation","default":null}},{"name":"image","optional":true,"typeAnnotation":{"type":"StringTypeAnnotation","default":null}},{"name":"imageColor","optional":true,"typeAnnotation":{"type":"Int32TypeAnnotation","default":0}},{"name":"displayInline","optional":true,"typeAnnotation":{"type":"BooleanTypeAnnotation","default":false}},{"name":"attributes","optional":true,"typeAnnotation":{"type":"ObjectTypeAnnotation","properties":[{"name":"destructive","optional":true,"typeAnnotation":{"type":"BooleanTypeAnnotation","default":false}},{"name":"disabled","optional":true,"typeAnnotation":{"type":"BooleanTypeAnnotation","default":false}},{"name":"hidden","optional":true,"typeAnnotation":{"type":"BooleanTypeAnnotation","default":false}}]}},{"name":"subactions","optional":true,"typeAnnotation":{"type":"ArrayTypeAnnotation","elementType":{"type":"ObjectTypeAnnotation","properties":[{"name":"id","optional":true,"typeAnnotation":{"type":"StringTypeAnnotation","default":null}},{"name":"title","optional":false,"typeAnnotation":{"type":"StringTypeAnnotation","default":null}},{"name":"titleColor","optional":true,"typeAnnotation":{"type":"Int32TypeAnnotation","default":0}},{"name":"subtitle","optional":true,"typeAnnotation":{"type":"StringTypeAnnotation","default":null}},{"name":"state","optional":true,"typeAnnotation":{"type":"StringTypeAnnotation","default":null}},{"name":"image","optional":true,"typeAnnotation":{"type":"StringTypeAnnotation","default":null}},{"name":"imageColor","optional":true,"typeAnnotation":{"type":"Int32TypeAnnotation","default":0}},{"name":"displayInline","optional":true,"typeAnnotation":{"type":"BooleanTypeAnnotation","default":false}},{"name":"attributes","optional":true,"typeAnnotation":{"type":"ObjectTypeAnnotation","properties":[{"name":"destructive","optional":true,"typeAnnotation":{"type":"BooleanTypeAnnotation","default":false}},{"name":"disabled","optional":true,"typeAnnotation":{"type":"BooleanTypeAnnotation","default":false}},{"name":"hidden","optional":true,"typeAnnotation":{"type":"BooleanTypeAnnotation","default":false}}]}}]}}}]}}},{"name":"actionsHash","optional":false,"typeAnnotation":{"type":"StringTypeAnnotation","default":null}},{"name":"title","optional":true,"typeAnnotation":{"type":"StringTypeAnnotation","default":null}},{"name":"themeVariant","optional":true,"typeAnnotation":{"type":"StringTypeAnnotation","default":null}},{"name":"shouldOpenOnLongPress","optional":true,"typeAnnotation":{"type":"BooleanTypeAnnotation","default":false}},{"name":"hitSlop","optional":false,"typeAnnotation":{"type":"ObjectTypeAnnotation","properties":[{"name":"top","optional":false,"typeAnnotation":{"type":"Int32TypeAnnotation","default":0}},{"name":"bottom","optional":false,"typeAnnotation":{"type":"Int32TypeAnnotation","default":0}},{"name":"left","optional":false,"typeAnnotation":{"type":"Int32TypeAnnotation","default":0}},{"name":"right","optional":false,"typeAnnotation":{"type":"Int32TypeAnnotation","default":0}}]}}],"commands":[]}}}}}
\ No newline at end of file
diff --git a/node_modules/@react-native-menu/menu/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml b/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml
new file mode 100644
index 0000000000000000000000000000000000000000..8363de6a947c2963fe61512b667878d798279c9b
--- /dev/null
+++ b/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.reactnativemenu" >
+
+    <uses-sdk android:minSdkVersion="23" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@react-native-menu/menu/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json b/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json
new file mode 100644
index 0000000000000000000000000000000000000000..d1e84d865b8d89cd252646fa62e9d61630871c09
--- /dev/null
+++ b/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.reactnativemenu",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/@react-native-menu/menu/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties b/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties
new file mode 100644
index 0000000000000000000000000000000000000000..1211b1ef0cfce28b279372c248df8e9a37685146
--- /dev/null
+++ b/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties
@@ -0,0 +1,6 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
+coreLibraryDesugaringEnabled=false
diff --git a/node_modules/@react-native-menu/menu/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json b/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json
new file mode 100644
index 0000000000000000000000000000000000000000..9e26dfeeb6e641a33dae4961196235bdb965b21b
--- /dev/null
+++ b/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/@react-native-menu/menu/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar b/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar
new file mode 100644
index 0000000000000000000000000000000000000000..b6ecc6c367c8b6fba934a27c2cd6465894bee6d9
Binary files /dev/null and b/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar differ
diff --git a/node_modules/@react-native-menu/menu/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt b/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt
new file mode 100644
index 0000000000000000000000000000000000000000..e69de29bb2d1d6434b8b29ae775ad8c2e48c5391
diff --git a/node_modules/@react-native-menu/menu/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties b/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000000000000000000000000000000000000..b2c89b56610f252ec2ea7d31e2f83842fc3844e7
--- /dev/null
+++ b/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Mon Jul 14 23:13:38 EEST 2025
diff --git a/node_modules/@react-native-menu/menu/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml b/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
new file mode 100644
index 0000000000000000000000000000000000000000..3165f3ed0c1fe20072de5cdeee550b9196dc942c
--- /dev/null
+++ b/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/build/generated/res/resValues/debug"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/@react-native-menu/menu/android/build/intermediates/incremental/mergeDebugShaders/merger.xml b/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
new file mode 100644
index 0000000000000000000000000000000000000000..11c95114e3e64c0c5d25e09f1ae65c018125dfcd
--- /dev/null
+++ b/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/main/shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/debug/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@react-native-menu/menu/android/build/intermediates/incremental/packageDebugAssets/merger.xml b/android/build/intermediates/incremental/packageDebugAssets/merger.xml
new file mode 100644
index 0000000000000000000000000000000000000000..33f1dd9058724aa5e3b9690a62a7f4f83e67969f
--- /dev/null
+++ b/android/build/intermediates/incremental/packageDebugAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/main/assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/debug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/build/intermediates/shader_assets/debug/compileDebugShaders/out"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@react-native-menu/menu/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt b/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt
new file mode 100644
index 0000000000000000000000000000000000000000..78ac5b8befe332ed81b26e325fdd18037d83cac5
--- /dev/null
+++ b/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/@react-native-menu/menu/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt b/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000000000000000000000000000000000000..14631e5ea6b5655d5fe7ab6f78e630a686f0a886
--- /dev/null
+++ b/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,7 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="com.reactnativemenu" >
+4
+5    <uses-sdk android:minSdkVersion="23" />
+6
+7</manifest>
diff --git a/node_modules/@react-native-menu/menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml b/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml
new file mode 100644
index 0000000000000000000000000000000000000000..8363de6a947c2963fe61512b667878d798279c9b
--- /dev/null
+++ b/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.reactnativemenu" >
+
+    <uses-sdk android:minSdkVersion="23" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@react-native-menu/menu/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json b/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json
new file mode 100644
index 0000000000000000000000000000000000000000..0637a088a01e8ddab3bf3fa98dbe804cbde1a0dc
--- /dev/null
+++ b/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/@react-native-menu/menu/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt b/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt
new file mode 100644
index 0000000000000000000000000000000000000000..08f4ebeab550e5b7670413a1d354bc2bba12a1d1
--- /dev/null
+++ b/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt
@@ -0,0 +1 @@
+0 Warning/Error
\ No newline at end of file
diff --git a/node_modules/@react-native-menu/menu/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt b/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt
new file mode 100644
index 0000000000000000000000000000000000000000..a5e55fa0195f28faad02c64578f66c8eafb06231
--- /dev/null
+++ b/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt
@@ -0,0 +1 @@
+com.reactnativemenu
diff --git a/node_modules/@react-native-menu/menu/android/build/kotlin/compileDebugKotlin/cacheable/dirty-sources.txt b/android/build/kotlin/compileDebugKotlin/cacheable/dirty-sources.txt
new file mode 100644
index 0000000000000000000000000000000000000000..a627fa88189c37283184a3d92d400dd289a71e73
--- /dev/null
+++ b/android/build/kotlin/compileDebugKotlin/cacheable/dirty-sources.txt
@@ -0,0 +1,8 @@
+/Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/main/java/com/reactnativemenu/MenuOnOpenEvent.kt
+/Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/main/java/com/reactnativemenu/MenuView.kt
+/Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/main/java/com/reactnativemenu/MenuOnCloseEvent.kt
+/Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/newarch/MenuViewManagerSpec.kt
+/Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/reactNativeVersionPatch/MenuViewManager/latest/com/reactnativemenu/MenuViewManager.kt
+/Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/main/java/com/reactnativemenu/MenuOnPressActionEvent.kt
+/Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/main/java/com/reactnativemenu/MenuPackage.kt
+/Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/main/java/com/reactnativemenu/MenuViewManagerBase.kt
\ No newline at end of file
diff --git a/node_modules/@react-native-menu/menu/android/build/kotlin/compileDebugKotlin/local-state/build-history.bin b/android/build/kotlin/compileDebugKotlin/local-state/build-history.bin
new file mode 100644
index 0000000000000000000000000000000000000000..4c5003af2cabaeed635f353ee6da2077c93d6eee
Binary files /dev/null and b/android/build/kotlin/compileDebugKotlin/local-state/build-history.bin differ
diff --git a/node_modules/@react-native-menu/menu/android/build/outputs/logs/manifest-merger-debug-report.txt b/android/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000000000000000000000000000000000000..3ad33a3991ecd698d48e71c2e927cef28d8d15de
--- /dev/null
+++ b/android/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,16 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/main/AndroidManifestNew.xml:1:1-2:12
+INJECTED from /Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/main/AndroidManifestNew.xml:1:1-2:12
+	package
+		INJECTED from /Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/main/AndroidManifestNew.xml
+	xmlns:android
+		ADDED from /Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/main/AndroidManifestNew.xml:1:11-69
+uses-sdk
+INJECTED from /Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/main/AndroidManifestNew.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/main/AndroidManifestNew.xml
+INJECTED from /Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/main/AndroidManifestNew.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/main/AndroidManifestNew.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/recepturko/node_modules/@react-native-menu/menu/android/src/main/AndroidManifestNew.xml
diff --git a/android/src/main/java/com/reactnativemenu/MenuViewManagerBase.kt b/android/src/main/java/com/reactnativemenu/MenuViewManagerBase.kt
index 0cd90f4741508a95a63198574228c244ad41afd8..d2d00ea7a820deafa4f93bdc54fd47e5f2ffedf6 100644
--- a/android/src/main/java/com/reactnativemenu/MenuViewManagerBase.kt
+++ b/android/src/main/java/com/reactnativemenu/MenuViewManagerBase.kt
@@ -204,10 +204,6 @@ abstract class MenuViewManagerBase : ReactClippingViewManager<MenuView>() {
   )
   abstract fun setBorderColor(view: ReactViewGroup, index: Int, color: Int?)
 
-  @ReactProp(name = ViewProps.OVERFLOW)
-  fun setOverflow(view: ReactViewGroup, overflow: String?) {
-    view.overflow = overflow
-  }
 
   @ReactProp(name = "backfaceVisibility")
   fun setBackfaceVisibility(view: ReactViewGroup, backfaceVisibility: String) {
