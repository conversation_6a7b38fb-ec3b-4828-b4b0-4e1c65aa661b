# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Expo/React Native Development Best Practices

This document outlines the best practices and conventions for developing mobile applications using Expo, React Native, and TypeScript.

## Code Style and Structure
- Write concise, technical TypeScript code.
- Use functional and declarative programming patterns.
- Avoid code duplication through iteration and modularization.
- Use descriptive variable names (e.g., `isLoading`, `hasError`).
- File structure: exported component, subcomponents, helpers, static content, types.
- Follow Expo's official documentation: [https://docs.expo.dev/](mdc:https:/docs.expo.dev)
- Prefer Expo/React Native packages over custom implementations.

## Naming Conventions
- Directories: lowercase with dashes (e.g., `components/auth-wizard`).
- Components: Favor named exports.

## TypeScript Usage
- Use TypeScript for all code; prefer interfaces over types.
- Avoid enums; use maps.
- Use functional components with TypeScript interfaces.
- Enable strict mode.

## Syntax and Formatting
- Use the `function` keyword for pure functions.
- Use concise syntax for conditionals and simple statements.
- Use declarative JSX.
- Use Prettier for formatting.

## UI and Styling
- Use Expo's built-in components.
- Implement responsive design with Flexbox and `useWindowDimensions`.
- Use or Nativewind/Tailwind CSS.
- Implement dark mode support with `useColorScheme`.
- Ensure high accessibility (a11y).
- Use `react-native-reanimated` and `react-native-gesture-handler` for animations/gestures.

## Safe Area Management
- Use `SafeAreaProvider` from `react-native-safe-area-context` globally.
- Wrap top-level components with `SafeAreaView`.
- Use `SafeAreaScrollView` for scrollable content.
- Rely on `SafeAreaView` and context hooks, avoid hardcoding padding/margins.

## Performance Optimization
- Minimize `useState` and `useEffect`; use context and reducers.
- Use `AppLoading` and `SplashScreen` for startup optimization.
- Optimize images (WebP, size data, lazy loading with `expo-image`).
- Use code splitting and lazy loading (React Suspense, dynamic imports).
- Profile performance with built-in tools.
- Memoize components (`React.memo`, `useMemo`, `useCallback`).

## Navigation
- Use `react-navigation` (stack, tab, drawer).
- Implement deep linking and universal links.
- Use dynamic routes with `expo-router`.

## State Management
- Use React Context and `useReducer` for global state.
- Use `react-query` for data fetching and caching.
- Consider Zustand or Redux Toolkit for complex state.
- Handle URL search parameters with `expo-linking`.

## Error Handling and Validation
- Use Zod for runtime validation.
- Implement error logging (Sentry).
- Prioritize error handling: early returns, avoid nested ifs, global error boundaries.
- Use `expo-error-reporter`.

## Testing
- Unit tests: Jest and React Native Testing Library.
- Integration tests: Detox.
- Use Expo's testing tools.
- Consider snapshot testing.

## Security
- Sanitize user inputs.
- Use `react-native-encrypted-storage` for sensitive data.
- Use HTTPS and proper authentication.
- Follow Expo's Security guidelines: [https://docs.expo.dev/guides/security/](mdc:https:/docs.expo.dev/guides/security)

## Internationalization (i18n)
- Use `react-native-i18n` or `expo-localization`.
- Support multiple languages and RTL layouts.
- Ensure text scaling and font adjustments.

## Key Conventions
- Use Expo's managed workflow.
- Prioritize Mobile Web Vitals.
- Use `expo-constants` for environment variables.
- Use `expo-permissions` for permissions.
- Implement `expo-updates` for OTA updates.
- Follow Expo's deployment practices: [https://docs.expo.dev/distribution/introduction/](mdc:https:/docs.expo.dev/distribution/introduction)
- Test thoroughly on iOS and Android.

## Workflow
-instead of building from scratch install packages/lybraries that provice out of the box solution
-use expo and react native packages/API/SDK instead of writting from scratch
-

## Project Overview

**Recepturko** is a React Native healthcare management mobile application built with Expo. It helps users manage medical information, medications, lab results, and healthcare providers with AI-powered features.

## Technology Stack

- **React Native 0.79.4** with **Expo 53** for cross-platform mobile development
- **TypeScript** with strict type checking
- **Expo Router** for file-based navigation
- **Supabase** for backend (PostgreSQL, auth, storage, edge functions)
- **Clerk** for user authentication
- **TanStack Query (React Query)** for server state management
- **Zustand** for client-side UI state
- **NativeWind** for Tailwind CSS styling
- **Zod** for schema validation with React Hook Form
- **i18next** for internationalization (English/Bulgarian)

## Development Commands

```bash
# Development server
bun start                 # Start Expo dev server
bun run ios              # Run on iOS simulator
bun run android          # Run on Android emulator
bun run web              # Run in web browser

# Code quality
bun run lint             # Run ESLint
bun test                 # Run Jest tests

# Dependencies
bun install              # Install all dependencies
```

## Project Architecture

### Routing Structure (Expo Router)
```
app/
├── (protected)/         # Authenticated routes
│   ├── (tabs)/         # Main app tabs (index, lab-results, notifications)
│   │   ├── (doctors-tabs)/  # Doctor management screens
│   │   └── (meds-tabs)/     # Medication management screens
│   ├── (ai-chat)/      # AI chat system
│   ├── modals/         # Modal screens for CRUD operations
│   └── settings.tsx
└── (public)/           # Public auth routes (sign-in, sign-up)
```

### Component Organization
- **Feature components**: `components/doctors/`, `components/patients/`, `components/medications/`
- **Reusable UI**: `components/ui/` (Button, Card, Input, DatePicker, etc.)
- **Common utilities**: `components/common/` (ImageCapture, DocumentPicker, Filter)
- **AI chat**: `components/ai-chat/`

### Data Layer Architecture
- **Custom hooks** for data operations: `hooks/use-doctors.ts`, `hooks/use-patients.ts`, etc.
- **React Query** for server state with optimistic updates
- **Supabase client** in `lib/supabase.ts` with generated TypeScript types
- **Database types** auto-generated in `types/database.types.ts`

## Key Development Patterns

### Data Management
- Use custom hooks (e.g., `useDoctors`, `usePatients`) for data operations
- React Query handles caching, background updates, and optimistic updates
- Supabase real-time subscriptions for live data
- Zod schemas for form validation and type safety

### Modal-Based CRUD Operations
- CRUD operations use modals from `app/(protected)/modals/`
- State managed via Zustand stores (e.g., `stores/modal-store.ts`)
- Consistent pattern: open modal → form → validate → submit → close

### Component Patterns
- UI components use NativeWind classes for styling
- Custom hooks encapsulate business logic
- TypeScript interfaces for all data structures
- Error boundaries and loading states

### AI Integration
- OCR functionality for medicine receipts and lab results
- Universal AI chat system with context awareness
- OpenAI API integration via Supabase Edge Functions

## Database Schema

Core entities: `doctors`, `patients`, `medicines`, `prescriptions`, `lab_results`, `ai_chat_sessions`

Row Level Security (RLS) enabled for all tables with user-based access control.

## Environment Setup

1. Install dependencies: `npm install`
2. Set up environment variables in `.env`
3. Configure Supabase connection
4. Configure Clerk authentication
5. Start development server: `npm start`

## Code Quality Standards

- **TypeScript strict mode** enabled
- **ESLint** with Expo configuration
- **Zod validation** for all forms and API data
- **React Query** for all server state
- **Custom hooks** for business logic
- **Modal patterns** for CRUD operations
- **Error handling** with proper user feedback
- **Loading states** for all async operations

## Testing

- **Jest** with `jest-expo` preset
- Test files located alongside components
- Focus on custom hooks and utility functions
- UI testing with React Native Testing Library patterns

## AI Features

- **OCR Processing**: Medicine receipt text extraction
- **Chat System**: Context-aware medical assistance
- **Lab Analysis**: AI-powered result interpretation
- Edge Functions handle AI API calls to maintain security

## Important Notes

- Always use generated TypeScript types from `types/database.types.ts`
- Follow existing modal patterns for new CRUD operations
- Use React Query for all server state management
- Implement proper error handling and loading states
- Maintain consistent styling with NativeWind classes
- Consider internationalization for user-facing text