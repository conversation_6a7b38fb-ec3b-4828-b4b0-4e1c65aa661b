import React, { useState } from 'react';
import { View, Text, Pressable, Platform, StyleProp, ViewStyle } from 'react-native';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@react-navigation/native';

interface DatePickerProps {
  label?: string;
  value: Date | undefined;
  onChange: (date: Date | undefined) => void;
  error?: string;
  placeholder?: string;
  style?: StyleProp<ViewStyle>;
  maximumDate?: Date;
  minimumDate?: Date;
}

const DatePicker: React.FC<DatePickerProps> = ({
  label,
  value,
  onChange,
  error,
  placeholder = 'Select date',
  style,
  maximumDate,
  minimumDate,
}) => {
  const theme = useTheme();
  const [show, setShow] = useState(false);
  const [tempDate, setTempDate] = useState<Date | undefined>(value);

  const formatDate = (date?: Date) => {
    if (!date) return '';
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const onIOSChange = (event: DateTimePickerEvent, selectedDate?: Date) => {
    if (event.type === 'set') {
      if (selectedDate) {
        onChange(selectedDate);
        setTempDate(selectedDate);
      } else {
        onChange(undefined);
        setTempDate(undefined);
      }
      setShow(false);
    }
  };

  const onAndroidChange = (_: DateTimePickerEvent, selectedDate?: Date) => {
    setShow(false);
    onChange(selectedDate);
  };

 
  return (
    <View className="mb-4" style={style}>
      {label && (
        <Text className="mb-1 text-sm font-medium text-foreground dark:text-neutral-300">{label}</Text>
      )}

      <Pressable
        onPress={() => setShow(true)}
        className={`
          w-full rounded-lg border px-4 h-12 flex-row justify-between items-center
          ${error 
            ? 'border-destructive bg-destructive/10 dark:border-destructive dark:bg-destructive/20' 
            : 'border-input bg-background dark:border-neutral-700 dark:bg-neutral-800'}
        `}
      >
        <Text
          className={`text-base ${value 
            ? 'text-foreground dark:text-neutral-50' 
            : 'text-muted-foreground dark:text-neutral-400'}`}
        >
          {value ? formatDate(value) : placeholder}
        </Text>
        <Ionicons 
          name="calendar" 
          size={20} 
          color={theme.colors.text} 
        />
      </Pressable>

      {error && (
        <Text className="mt-1 text-sm text-destructive dark:text-red-400">{error}</Text>
      )}

      {show && Platform.OS === 'android' && (
        <DateTimePicker
          value={value || new Date()}
          mode="date"
          display="default"
          onChange={onAndroidChange}
          maximumDate={maximumDate}
          minimumDate={minimumDate}
        />
      )}

      {Platform.OS === 'ios' && show && (
        <DateTimePicker
          value={tempDate || value || new Date()}
          mode="date"
          display="inline"
          onChange={onIOSChange}
          maximumDate={maximumDate}
          minimumDate={minimumDate}
        />
      )}
    </View>
  );
};

export default DatePicker; 