import React, { useState } from 'react';
import { View, Text, Pressable, StyleProp, ViewStyle, ActivityIndicator } from 'react-native';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@react-navigation/native';

interface CardProps {
  title: string;
  subtitle?: string;
  content?: React.ReactNode;
  footer?: React.ReactNode;
  onPress?: () => void;
  style?: StyleProp<ViewStyle>;
  children?: React.ReactNode;
  rightAction?: React.ReactNode;
  imageUrl?: string | null;
  imageLoading?: boolean;
  fallbackImage?: number;
}

const Card: React.FC<CardProps> = ({
  title,
  subtitle,
  content,
  footer,
  onPress,
  style,
  children,
  rightAction,
  imageUrl,
  imageLoading,
  fallbackImage,
}) => {
  const theme = useTheme();
  const CardContainer = onPress ? Pressable : View;
  const [imageLoadError, setImageLoadError] = useState(false);

  const handleImageError = () => {
    console.log('Card: Image load error for URL:', imageUrl);
    setImageLoadError(true);
  };

  // Reset error state if imageUrl changes
  React.useEffect(() => {
    if (imageUrl) {
      setImageLoadError(false);
    }
  }, [imageUrl]);

  return (
    <CardContainer
      onPress={onPress}
      className="bg-card border border-border shadow-sm mb-3 overflow-hidden dark:bg-neutral-800 dark:border-neutral-700"
      style={style}
    >
      <View className="flex-row p-4">
        {(imageUrl || imageLoading) && (
          <View className="w-16 h-16 mr-3 justify-center items-center bg-muted rounded dark:bg-neutral-700">
            {imageLoading ? (
              <ActivityIndicator size="small" className="text-muted-foreground dark:text-neutral-400" />
            ) : imageLoadError || !imageUrl ? (
              fallbackImage ? (
                <Image
                  source={fallbackImage}
                  style={{ width: 64, height: 64, borderRadius: 4 }}
                  contentFit="cover"
                />
              ) : (
                <Ionicons name="medkit-outline" size={24} color={theme.colors.text} />
              )
            ) : (
              <Image
                source={{ uri: imageUrl }}
                style={{ width: 64, height: 64, borderRadius: 4 }}
                contentFit="cover"
                placeholder={{ blurhash: "LKO2?U%2Tw=w]~RBVZRi};RPxuwH" }}
                transition={500}
                onError={handleImageError}
              />
            )}
          </View>
        )}

        <View className="flex-1">
          <View className="flex-row justify-between items-start">
            <View className="flex-1 mr-2">
              <Text className="text-lg font-semibold text-card-foreground dark:text-neutral-100" numberOfLines={2}>{title}</Text>
              {subtitle && (
                <Text className="text-sm text-muted-foreground dark:text-neutral-400 mt-1">{subtitle}</Text>
              )}
            </View>
            {rightAction && (
              <View className="justify-center ml-2">{rightAction}</View>
            )}
          </View>

          {content && <View className="mt-3">{content}</View>}
          {children && <View className="mt-3">{children}</View>}
        </View>
      </View>

      {footer && (
        <View className="px-4 py-3 bg-muted border-t border-border dark:bg-neutral-800 dark:border-neutral-700">
          {footer}
        </View>
      )}
    </CardContainer>
  );
};

export default Card; 