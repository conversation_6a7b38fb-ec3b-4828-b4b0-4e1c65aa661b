import React, { useState, useEffect, useLayoutEffect, useCallback } from 'react';
import { View, Text, Alert, Pressable, Dimensions } from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigation, useRouter } from 'expo-router';
import { useTheme } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useModalCreationStore } from '@/store/modalCreationStore';
import * as Linking from 'expo-linking';

import ControlledInput from '../ControlledInput';
import DatePicker from '../ui/DatePicker';
import RouterSelectInput, { RouterSelectOption } from '../common/RouterSelectInput';
import { Button } from '../ui/Button';
import ImageCapture, { ImageCaptureResult } from '../common/ImageCapture';
import { useImage } from '@/store/imageStore';
import DocumentPickerComponent, { DocumentResult } from '../common/DocumentPicker';
import { useSupabaseStorageUrl } from '@/hooks/useSupabaseStorageUrl';
import { usePatients, Patient } from '@/hooks/entities/usePatients';
import { useLabResults } from '@/hooks/entities/useLabResults';
import { labResultFormSchema, LabResultFormData } from '@/schema/lab-result';
import { useAppAlerts } from '@/hooks/useAppAlerts';
import { useTranslation } from '@/hooks/useTranslation';
import { OpenAIOcrResults } from '@/hooks/useOpenAIImageOcr';
import Animated, {
  interpolate,
  SlideInDown,
  useAnimatedRef,
  useAnimatedStyle,
  useScrollViewOffset
} from 'react-native-reanimated';

export interface LabResultWithImage extends Partial<LabResultFormData> {
  id?: string;
  created_at?: string | null;
  updated_at?: string | null;
  user_id?: string | null;
  image_path?: string | null; // For primary image
  image_bucket_id?: string | null; // For primary image
  captured_pdf_path?: string | null; // For captured PDF (webview or local)
  captured_pdf_bucket_id?: string | null; // For captured PDF
  // file_name and image_metadata seem less relevant if we have specific paths
}

interface LabResultFormProps {
  initialValues?: LabResultWithImage;
  onSubmit: (
    data: LabResultFormData,
    primaryFileUri?: string, // For primary image (image_path)
    deletePrimaryFile?: boolean,
    localCapturedPdfUri?: string, // For PDF to go into captured_pdf_path
    deleteExistingCapturedPdf?: boolean // To delete PDF from captured_pdf_path
  ) => void;
  isLoading?: boolean;
  startWithCamera?: boolean;
  isEditing: boolean;
  labResultId?: string;
  capturedImageUri?: string; // For pre-captured images from header
}

const LabResultForm: React.FC<LabResultFormProps> = ({
  initialValues,
  onSubmit,
  isLoading = false,
  startWithCamera = false, // For primary image
  isEditing,
  labResultId,
  capturedImageUri, // For pre-captured images from header
}) => {
  const theme = useTheme();
  const navigation = useNavigation();
  const router = useRouter();
  const { confirm, showSuccess, showError } = useAppAlerts();
  const { t } = useTranslation();
  const { useDeleteLabResult } = useLabResults();
  const deleteLabResultMutation = useDeleteLabResult();
  const { newlyCreatedPatientId, resetPatientCreation } = useModalCreationStore();

  // Image state from store
  const storeKey = `labresult-primary-${labResultId || 'new'}`;
  const {
    uri: primaryImageUri,
    actions: imageActions
  } = useImage(storeKey);
  
  // Local state for delete flag and new local URI tracking
  const [newLocalPrimaryImageUri, setNewLocalPrimaryImageUri] = useState<string | null>(null);
  const [deletePrimaryFile, setDeletePrimaryFile] = useState(false);

  // State for LOCALLY PICKED PDF (to go to captured_pdf_path)
  const [localCapturedPdfUri, setLocalCapturedPdfUri] = useState<string | null>(null);
  const [localCapturedPdfName, setLocalCapturedPdfName] = useState<string | null>(null);

  // State for deleting EXISTING (already saved) CAPTURED PDF (from captured_pdf_path)
  const [deleteExistingCapturedPdf, setDeleteExistingCapturedPdf] = useState(false);

  const { control, handleSubmit, setValue, formState: { errors } } = useForm<LabResultFormData>({
    resolver: zodResolver(labResultFormSchema),
    defaultValues: initialValues ? {
      lab_name: initialValues.lab_name,
      password: initialValues.password,
      patient_id: initialValues.patient_id,
      patient_reference_id: initialValues.patient_reference_id === null ? undefined : initialValues.patient_reference_id,
      patient_name: initialValues.patient_name || '',
      result_date: initialValues.result_date,
      website: initialValues.website || '',
      phone_number1: initialValues.phone_number1 || '',
      phone_number2: initialValues.phone_number2 || '',
      phone_number3: initialValues.phone_number3 || '',
    } : {
      lab_name: '',
      password: '',
      patient_id: '',
      patient_reference_id: undefined,
      patient_name: '',
      result_date: undefined,
      website: '',
      phone_number1: '',
      phone_number2: '',
      phone_number3: '',
     },
  });

  const { useFetchPatients } = usePatients();
  const { data: patientsData, isLoading: isLoadingPatients, refetch: refetchPatients } = useFetchPatients();

  const patientOptions = React.useMemo((): RouterSelectOption[] => {
    // Return all patients, filtering happens in the modal now
    return patientsData?.map((p: Patient): RouterSelectOption => ({
      id: p.id,
      label: p.name,
      subtitle: `ID: ${p.id.substring(0, 8)}...`,
      itemType: 'patient',
      data: p,
    })) || [];
  }, [patientsData]);

  // --- Handlers for PRIMARY IMAGE attachment ---
  const handlePrimaryImageCaptured = useCallback((result: ImageCaptureResult) => {
    setNewLocalPrimaryImageUri(result.uri);
    setDeletePrimaryFile(false);
    // Apply OCR results if available (we know they're lab results since ocrType='lab')
    if (result.ocrResults) {
        const labResults = result.ocrResults as OpenAIOcrResults;
        if (labResults.labName) setValue('lab_name', labResults.labName);
        if (labResults.resultDate) setValue('result_date', labResults.resultDate);
        if (labResults.patientName) setValue('patient_name', labResults.patientName);
        if (labResults.patientId) setValue('patient_id', labResults.patientId);
        if (labResults.website) setValue('website', labResults.website);
        if (labResults.phoneNumber1) setValue('phone_number1', labResults.phoneNumber1);
        if (labResults.phoneNumber2) setValue('phone_number2', labResults.phoneNumber2);
        if (labResults.phoneNumber3) setValue('phone_number3', labResults.phoneNumber3);
        if (labResults.password) setValue('password', labResults.password);
        showSuccess('Data extracted from image. Please review.', 'OCR Success');
    }
  }, [setValue, showSuccess]);

  const handleRemovePrimaryImage = () => {
    imageActions.setUri(null);
    setNewLocalPrimaryImageUri(null);
    setDeletePrimaryFile(true); // Signal to delete existing if there was one
  };

  // --- Handlers for LOCALLY PICKED PDF (for captured_pdf_path) ---
  const handleLocalPdfPicked = (document: DocumentResult) => {
    if (document.mimeType && !document.mimeType.includes('pdf')) {
        showError('Please select a PDF file.', 'Invalid File Type');
        return;
    }
    setLocalCapturedPdfUri(document.uri);
    setLocalCapturedPdfName(document.name);
    setDeleteExistingCapturedPdf(false); // If user picks a new PDF, they likely don't want to delete the old one AND this new one
  };

  const handleRemoveLocalPickedPdf = () => {
    setLocalCapturedPdfUri(null);
    setLocalCapturedPdfName(null);
    // No need to set deleteExistingCapturedPdf here, as this is for a *newly picked* unsaved PDF.
    // If the user wanted to delete an *already saved* captured PDF, they'd use the other remove button.
  };

  // --- Handler for removing EXISTING (SAVED) CAPTURED PDF ---
  const handleRemoveExistingCapturedPdfPress = () => {
    confirm({
      title: 'Remove Saved PDF',
      message: 'Are you sure you want to remove the previously saved PDF? This action will take effect when you save the lab result.',
      confirmText: 'Remove',
      style: 'destructive',
      onConfirm: () => {
        setDeleteExistingCapturedPdf(true);
        setLocalCapturedPdfUri(null); // Clear any newly picked local PDF if user now decides to remove the saved one
        setLocalCapturedPdfName(null);
        showSuccess('Saved PDF marked for removal. Save the form to apply.');
      },
    });
  };

  const onFormSubmit = (data: LabResultFormData) => {
    onSubmit(
      data,
      newLocalPrimaryImageUri || undefined,
      deletePrimaryFile,
      localCapturedPdfUri || undefined,
      deleteExistingCapturedPdf
    );
    // Reset relevant states after submission attempt if needed, or rely on modal dismiss
    // setDeleteExistingCapturedPdf(false); // Example reset
  };

  // URL for initial PRIMARY IMAGE
  const {
    url: initialPrimaryImageUrl,
    isLoading: isLoadingPrimaryUrl,
    error: primaryUrlError
  } = useSupabaseStorageUrl({
    bucketId: initialValues?.image_bucket_id || 'labresults',
    path: initialValues?.image_path,
  });

  // URL for initial EXISTING CAPTURED PDF (from webview or previous local upload)
  const {
    url: initialCapturedPdfUrl,
    isLoading: isLoadingCapturedPdfUrl,
    error: capturedPdfUrlError
  } = useSupabaseStorageUrl({
    bucketId: initialValues?.captured_pdf_bucket_id || 'labresults',
    path: initialValues?.captured_pdf_path,
  });

  // Effect to set initial state for PRIMARY IMAGE
  useEffect(() => {
    if (capturedImageUri && !isEditing) {
      // Handle pre-captured image from header
      imageActions.setUri(capturedImageUri);
      setNewLocalPrimaryImageUri(capturedImageUri);
      setDeletePrimaryFile(false);
    } else if (initialValues?.image_path && initialPrimaryImageUrl) {
      imageActions.setUri(initialPrimaryImageUrl);
      setDeletePrimaryFile(false);
    } else if (!initialValues?.image_path) {
      imageActions.setUri(null);
    }
  }, [capturedImageUri, initialValues?.image_path, initialPrimaryImageUrl, isEditing, startWithCamera, imageActions]);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      imageActions.cleanup();
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);


  // Effect for patient auto-selection (no changes)
  useEffect(() => {
    if (newlyCreatedPatientId) {
      if (patientsData && patientsData.some((p: Patient) => p.id === newlyCreatedPatientId)) {
        setValue("patient_reference_id", newlyCreatedPatientId);
        resetPatientCreation();
      } else if (!isLoadingPatients) {
        refetchPatients()
          .then(refetchResult => {
            if (refetchResult.data && refetchResult.data.some((p: Patient) => p.id === newlyCreatedPatientId)) {
              setValue("patient_reference_id", newlyCreatedPatientId);
            }
            resetPatientCreation();
          })
          .catch(error => {
            console.error("Error refetching patients for LabResultForm:", error);
            resetPatientCreation(); // Reset on error too
          });
      }
    }
  }, [newlyCreatedPatientId, patientsData, setValue, resetPatientCreation, refetchPatients, isLoadingPatients]);

  const handleDeletePress = useCallback(() => {
    if (!labResultId) return;

    confirm({
      title: t('alerts.confirmDelete'),
      message: t('alerts.deleteLabResultMessage'),
      confirmText: t('common.delete'),
      style: 'destructive',
      onConfirm: async () => {
        try {
          await deleteLabResultMutation.mutateAsync({ id: labResultId });
          showSuccess(t('alerts.deleteSuccess'), t('common.delete'));
          router.dismiss(); 
        } catch (error: any) {
          console.error('Delete Error from LabResultForm:', error);
          showError(error.message || t('labResults.deleteError'));
        }
      },
    });
  }, [labResultId, confirm, deleteLabResultMutation, showSuccess, showError, router, t]);

  useLayoutEffect(() => {
    navigation.setOptions({
      // title: isEditing ? t('buttons.editLabResult') : t('buttons.addLabResult'),
      headerBackground: () => (
        <Animated.View className="flex-1 bg-background dark:bg-neutral-900" style={headerAnimatedStyle} />
      ),
      headerRight: () => (
        isEditing && labResultId && (
          <View className="bg-white dark:bg-neutral-900 rounded-full p-3 items-center justify-center">
          <Pressable onPress={handleDeletePress} disabled={deleteLabResultMutation.isPending || isLoading}>
            <Ionicons
              name="trash"
              size={24}
              color={(deleteLabResultMutation.isPending || isLoading) ? theme.colors.text : theme.colors.notification} 
            />
          </Pressable>
          </View>
        )
      ),
      headerLeft: () => (
        <View className="bg-white dark:bg-neutral-900 rounded-full p-3 items-center justify-center">
          <Pressable onPress={() => router.back()}>
            <Ionicons name="chevron-back-outline" size={24} color={theme.colors.text} />
          </Pressable>
        </View>
      ),
    });
  }, [navigation, isEditing, labResultId, deleteLabResultMutation.isPending, isLoading, handleDeletePress, t]);

  const displayInitialCapturedPdfName = initialValues?.captured_pdf_path?.split('/').pop() || 'Previously Saved PDF';

  // Animation setup
  const IMG_HEIGHT = 300;
  const width = Dimensions.get('window').width;
  const scrollRef = useAnimatedRef<Animated.ScrollView>();

  const scrollOffset = useScrollViewOffset(scrollRef);
  const imageAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{
        translateY: interpolate(scrollOffset.value,
          [-IMG_HEIGHT, 0, IMG_HEIGHT],
          [IMG_HEIGHT/2, 0, IMG_HEIGHT*0.75]
        )
      },
      {
        scale: interpolate(scrollOffset.value,
          [-IMG_HEIGHT, 0, IMG_HEIGHT],
          [2, 1, 2]
        ),
      }
    ],
    };
  });

  const headerAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: interpolate(scrollOffset.value, 
        [0, IMG_HEIGHT /1.5],
        [0, 1]
      ),
    };
  });

  return (
    <View className="flex-1 bg-background dark:bg-neutral-900">
      <Animated.ScrollView
        ref={scrollRef}
        className="flex-1 bg-background dark:bg-neutral-900"
        contentContainerStyle={{ paddingBottom: 100 }}
        keyboardShouldPersistTaps="handled"
        scrollEventThrottle={16}
      >
        <ImageCapture
            storeKey={storeKey}
            initialImage={capturedImageUri || primaryImageUri || initialPrimaryImageUrl}
            onImageCaptured={handlePrimaryImageCaptured}
            onRemove={handleRemovePrimaryImage}
            onError={(err) => showError(err.message, 'Image Error')}
            enableOcr={true}
            ocrType="lab"
            triggerOcrOnInitial={!!capturedImageUri}
            placeholderText={t('placeholders.tapToAddLabImage')}
            placeholderImageSource={require('../../assets/images/defaults/lab-result-square.jpeg')}
            imageHeight={IMG_HEIGHT}
            imageWidth={width}
            animatedImageStyle={imageAnimatedStyle}
        />

        {isLoadingPrimaryUrl && <Text className="text-sm text-muted-foreground dark:text-neutral-400 mt-1 text-center">Loading initial image...</Text>}
        {primaryUrlError && !newLocalPrimaryImageUri && initialValues?.image_path && (
            <Text className="text-sm text-destructive dark:text-red-400 mt-1 text-center">
                Failed to load initial images. Check connection or try re-selecting.
            </Text>
        )}

        <View className="flex-1 px-4 bg-background dark:bg-neutral-900 py-4">
        {/* --- PDF Document Attachment Section (for captured_pdf_path) --- */}
        <View className="mb-4 p-3 border border-border dark:border-neutral-700 rounded-lg bg-card dark:bg-neutral-800">
          <Text className="text-base font-semibold text-foreground dark:text-neutral-100 mb-2">Attach PDF Document (Optional)</Text>
          {!localCapturedPdfUri && (!initialValues?.captured_pdf_path || deleteExistingCapturedPdf) && (
             <DocumentPickerComponent
                onDocumentPicked={handleLocalPdfPicked}
                allowedTypes={['application/pdf']}
                buttonText={t('placeholders.selectPdfDocument')}
                onError={(error) => Alert.alert(t('alerts.documentError'), error.message)}
            />
          )}

          {/* Display newly picked local PDF */}
          {localCapturedPdfUri && localCapturedPdfName && (
            <View className="mt-2 p-2 bg-muted dark:bg-neutral-750 rounded-md">
              <View className="flex-row items-center justify-between">
                <Ionicons name="document-text-outline" size={24} className="text-foreground dark:text-neutral-300 mr-2" />
                <Text className="flex-1 text-sm text-foreground dark:text-neutral-300" numberOfLines={1}>
                  {localCapturedPdfName}
                </Text>
                <Pressable onPress={handleRemoveLocalPickedPdf} className="p-1 ml-2">
                  <Ionicons name="close-circle-outline" size={20} color={theme.colors.notification} />
                </Pressable>
              </View>
                {/* Optional: Button to view localCapturedPdfUri if possible */}
            </View>
          )}

          {/* Display existing/initial captured PDF if no new local one is picked OR if the new local one is same as existing */}
          {initialValues?.captured_pdf_path && !localCapturedPdfUri && !deleteExistingCapturedPdf && (
            <View className="mt-2">
              <Text className="text-sm font-medium text-foreground dark:text-neutral-300 mb-1">Previously Saved PDF:</Text>
              <View className="flex-row items-center justify-between p-2 bg-muted dark:bg-neutral-750 rounded-md">
                 <Ionicons name="document-text-outline" size={24} className="text-foreground dark:text-neutral-300 mr-2" />
                <Text className="flex-1 text-sm text-foreground dark:text-neutral-300" numberOfLines={1}>
                  {displayInitialCapturedPdfName}
                </Text>
                <Pressable onPress={handleRemoveExistingCapturedPdfPress} className="p-1 ml-2">
                  <Ionicons name="trash-outline" size={20} color={theme.colors.notification} />
                </Pressable>
              </View>
              {isLoadingCapturedPdfUrl ? (
                <Button
                    title={t('status.loading')}
                    variant='outline'
                    disabled={true}
                    onPress={() => {}}
                    icon="hourglass-outline" />
              ) : initialCapturedPdfUrl ? (
                <Button
                    title={t('buttons.viewPDF')}
                    variant='outline'
                    onPress={() => initialCapturedPdfUrl && Linking.openURL(initialCapturedPdfUrl)}
                    icon="eye-outline" />
              ) : null}
              {capturedPdfUrlError && (
                 <Text className="text-xs text-destructive dark:text-red-400 mt-1">Failed to load URL for saved PDF.</Text>
              )}
            </View>
          )}
           {/* Message if existing PDF is marked for deletion */}
          {deleteExistingCapturedPdf && initialValues?.captured_pdf_path && !localCapturedPdfUri && (
            <View className="my-2 p-3 border border-destructive dark:border-red-700 rounded-lg bg-destructive/10 dark:bg-red-900/20">
                <Text className="text-sm text-destructive dark:text-red-400">
                    The previously saved PDF has been marked for removal and will be deleted when you save.
                </Text>
            </View>
        )}
        </View>

        {/* --- Other Form Fields (Lab Name, Password, Patient, etc.) --- */}
        <ControlledInput
          control={control}
          name="lab_name"
          label="Lab Name"
          placeholder="Enter lab name"
          leftIcon="business"
          autoCapitalize="words"
        />
        <ControlledInput
          control={control}
          name="patient_name"
          label="Patient Name from Lab Result (Optional)"
          placeholder="Enter patient's name as it appears on the lab result"
          leftIcon="person"
          autoCapitalize="words"
        />
        <ControlledInput
          control={control}
          name="patient_id"
          label="Patient ID (Required)"
          placeholder="Enter patient ID from lab result"
          leftIcon="person"
          autoCapitalize="none"
        />
        <ControlledInput
          control={control}
          name="password"
          label="Password"
          placeholder="Enter lab password"
          leftIcon="key"
          secureTextEntry
        />

        <View className="mb-4">
          
          <Controller
            control={control}
            name="patient_reference_id"
            render={({ field: { onChange, value } }) => (
              <RouterSelectInput
                label="Patient"
                options={patientOptions}
                selectedOption={patientOptions.find(option => option.id === value)}
                onSelect={(option) => onChange(option.id)}
                isLoading={isLoadingPatients}
                modalTitle="Select Patient"
                placeholder="Select patient"
                error={errors.patient_reference_id?.message}
                showCreateNewButton={true}
                onCreateNew={() => router.push('/modals/modal-patient')}
                createText="Add New Patient"
                modalPlaceholder="Search patients..."
                clearable={true}
              />
            )}
          />
        </View>

        <View className="mb-4">
          <Controller
            control={control}
            name="result_date"
            render={({ field: { onChange, value } }) => (
              <DatePicker
                label="Result Date"
                value={value ? new Date(value) : undefined}
                onChange={(date) => onChange(date?.toISOString())}
                placeholder="Select result date"
                maximumDate={new Date()}
              />
            )}
          />
        </View>

        <ControlledInput
          control={control}
          name="website"
          label="Website (Optional)"
          placeholder="Enter lab website"
          leftIcon="globe"
          autoCapitalize="none"
          keyboardType="url"
        />

        <ControlledInput
          control={control}
          name="phone_number1"
          label="Phone Number 1 (Optional)"
          placeholder="Enter phone number"
          leftIcon="call"
          keyboardType="phone-pad"
        />

        <ControlledInput
          control={control}
          name="phone_number2"
          label="Phone Number 2 (Optional)"
          placeholder="Enter alternate phone number"
          leftIcon="call"
          keyboardType="phone-pad"
        />

        <ControlledInput
          control={control}
          name="phone_number3"
          label="Phone Number 3 (Optional)"
          placeholder="Enter another phone number"
          leftIcon="call"
          keyboardType="phone-pad"
        />
        </View>
      </Animated.ScrollView>

      <Animated.View className="absolute bottom-5 left-4 right-4 h-20 py-3 px-4" entering={SlideInDown.delay(300)}>
        <Button
          title={initialValues ? 'Update Lab Result' : 'Add Lab Result'}
          onPress={handleSubmit(onFormSubmit)}
          variant="primary"
          isLoading={isLoading}
          icon={initialValues ? "save" : "add-circle"}
          fullWidth={false}
        />
      </Animated.View>
    </View>
  );
};

export default LabResultForm;