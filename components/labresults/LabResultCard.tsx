import React from 'react';
import { View, Text, Alert, Pressable, ActivityIndicator } from 'react-native';
import { format } from 'date-fns';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@react-navigation/native';
import { router } from 'expo-router';
import { Image } from 'expo-image';
import * as Linking from 'expo-linking';
import { LabResultWithPatient } from '@/hooks/entities/useLabResults';
import { useSupabaseStorageUrl } from '@/hooks/useSupabaseStorageUrl';
import { useAppAlerts } from '@/hooks/useAppAlerts';
import { Button } from '../ui/Button';
import { useAnalyses } from '@/hooks/entities/useAnalyses';
import { useChatSessions } from '@/hooks/entities/useChatSessions';
import { useTranslation } from '@/hooks/useTranslation';

interface LabResultCardProps {
  result: LabResultWithPatient;
  onPress?: () => void;
}

const formatDate = (dateString: string | null): string => {
  if (!dateString) return 'N/A';
  try {
    return format(new Date(dateString), 'dd/MM/yy');
  } catch {
    return 'Invalid date';
  }
};

const InfoRow: React.FC<{ icon: keyof typeof Ionicons.glyphMap; text: string | null | undefined; color?: string; numberOfLines?: number; }> = ({ icon, text, color = 'text-gray-600 dark:text-gray-400', numberOfLines = 1 }) => {
  const theme = useTheme();
  return (
    <View className="flex-row items-start mt-1">
      <Ionicons name={icon} size={14} color={theme.colors.text} style={{marginTop: 2}} />
      <Text className={`text-xs ml-1.5 ${color} flex-1`} numberOfLines={numberOfLines}>{text || 'N/A'}</Text>
    </View>
  );
};

const LabResultCard: React.FC<LabResultCardProps> = ({ result, onPress }) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const { confirm } = useAppAlerts();

  // Use TanStack Query hooks
  const { useFetchLabResultAnalysis } = useAnalyses();
  const { useFetchSessionByContext, useCreateChatSession } = useChatSessions();

  // Query for existing analysis
  const {
    data: analysis,
    isLoading: isCheckingAnalysis
  } = useFetchLabResultAnalysis(result.id);

  // Enhanced session fetching for existing analysis
  const {
    data: existingSession,
    isLoading: isLoadingSession
  } = useFetchSessionByContext('lab_result', result.id);

  // Mutations
  const createSessionMutation = useCreateChatSession();

  const hasAnalysis = !!analysis;
  const hasExistingSession = !!existingSession;

  const imagePath = result.image_path;
  const bucketId = result.image_bucket_id;

  const { url: imageUrl, isLoading: isLoadingUrl } = useSupabaseStorageUrl({
    path: imagePath,
    bucketId: bucketId || 'labresults',
  });

  // Get URL for captured PDF if it exists
  const { url: capturedPdfUrl } = useSupabaseStorageUrl({
    path: result.captured_pdf_path,
    bucketId: result.captured_pdf_bucket_id || 'labresults',
  });

  // Check if there's a PDF attachment (either in image_path or captured_pdf_path)
  const hasPdfAttachment = !!result.captured_pdf_path || (result.image_path && result.image_path.toLowerCase().includes('.pdf'));

  const handleCheckResultsPress = () => {
    if (!result.website) return;

    let url = result.website;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url;
    }

    confirm({
      title: t('misc.openExternalWebsite'),
      message: t('misc.externalWebsiteMessage'),
      confirmText: t('buttons.proceed'),
      cancelText: t('common.cancel'),
      onConfirm: () => {
        router.push({
          pathname: '/modals/lab-result-webview',
          params: {
            websiteUrl: url,
            patientId: result.patient_id,
            password: result.password,
            labResultId: result.id,
          },
        });
      },
    });
  };

  const handleViewLabResult = () => {
    // Determine which PDF to open
    let pdfUrl: string | null = null;
    
    if (result.captured_pdf_path) {
      pdfUrl = capturedPdfUrl;
    } else if (result.image_path && result.image_path.toLowerCase().includes('.pdf')) {
      pdfUrl = imageUrl;
    }

    if (pdfUrl) {
      Linking.openURL(pdfUrl).catch(err => {
        console.error('Failed to open PDF:', err);
        Alert.alert(t('common.error'), t('alerts.failedToOpenPdf'));
      });
    } else {
      Alert.alert(t('common.error'), t('alerts.pdfNotFound'));
    }
  };

  const handleAnalyzeWithAI = async () => {
    try {
      // Prevent duplicate analysis
      if (createSessionMutation.isPending) {
        console.log("Analysis already in progress, ignoring duplicate request");
        return;
      }

      // If we have existing session, navigate to it
      if (hasExistingSession && existingSession) {
        router.push(`/(ai-chat)/${existingSession.id}` as any);
        return;
      }

      // If we already have analysis with session, navigate to it
      if (hasAnalysis && analysis?.session_id) {
        router.push(`/(ai-chat)/${analysis.session_id}` as any);
        return;
      }

      // Create session
      const session = await createSessionMutation.mutateAsync({
        type: 'lab_result',
        contextId: result.id,
        title: `${result.lab_name} Analysis`,
      });

      // Navigate immediately
      router.push(`/(ai-chat)/${session.id}` as any);
    } catch (error) {
      console.error("Failed to create session:", error);
      Alert.alert(t('common.error'), t('aiChat.failedToOpenChat'));
    }
  };

  // Enhanced button logic with proper loading states
  const getAIButtonText = () => {
    if (isCheckingAnalysis || isLoadingSession) return t('status.loading');
    if (createSessionMutation.isPending) return t('status.analyzing');
    if (hasExistingSession || hasAnalysis) return t('aiChat.continueChat');
    return t('buttons.analyzeWithAI');
  };

  const getAIButtonIcon = () => {
    if (isCheckingAnalysis || isLoadingSession) return "hourglass-outline";
    if (createSessionMutation.isPending) return "hourglass-outline";
    if (hasExistingSession || hasAnalysis) return "chatbubble-outline";
    return "sparkles-outline";
  };

  // Check if button should be disabled
  const isAIButtonDisabled = () => {
    return isCheckingAnalysis || 
           isLoadingSession || 
           createSessionMutation.isPending;
  };


  // Mask password for display
  const formatPassword = () => result.password ? '••••••••' : null;

  return (
    <Pressable onPress={onPress} className="bg-white dark:bg-neutral-800 rounded-xl shadow-sm border border-gray-200 dark:border-neutral-700 flex-1 m-1 overflow-hidden flex flex-col">
      {/* Image Container */}
      <View className="relative" style={{ aspectRatio: 16 / 9 }}>
        {isLoadingUrl ? (
          <View className="h-full bg-gray-100 dark:bg-neutral-700 justify-center items-center">
            <ActivityIndicator />
          </View>
        ) : imageUrl ? (
          <Image source={{ uri: imageUrl }} style={{ height: '100%', width: '100%' }} contentFit="cover" />
        ) : (
          <Image 
            source={require('../../assets/images/defaults/lab-result-square.jpeg')} 
            style={{ height: '100%', width: '100%' }} 
            contentFit="cover" 
          />
        )}

        {/* Overlays */}
        <View className="absolute top-0 left-0 right-0 p-2 flex-row justify-between">
          <View className="flex-row items-center bg-black/50 px-2 py-1 rounded-full">
            <Ionicons name="calendar-outline" size={12} color="white" />
            <Text className="text-white text-xs font-medium ml-1">Result: {formatDate(result.result_date)}</Text>
          </View>
          {hasPdfAttachment && (
            <View className="bg-green-500/70 px-2 py-1 rounded-full">
              <View className="flex-row items-center">
                <Ionicons name="document-attach" size={12} color="white" />
                <Text className="text-white text-xs font-medium ml-1">PDF</Text>
              </View>
            </View>
          )}
        </View>
        <View className="absolute bottom-0 left-0 right-0 p-2 flex-row justify-between">
          <View className="flex-row items-center bg-black/50 px-2 py-1 rounded-full">
            <Ionicons name="person-outline" size={12} color="white" />
            <Text className="text-white text-xs font-medium ml-1" numberOfLines={1}>
              {result.patients?.name || result.patient_name || 'N/A'}
            </Text>
          </View>
        </View>
      </View>

      {/* Content */}
      <View className="p-3 flex-1 flex flex-col justify-between">
        <View className="pb-3">
          <Text className="text-base font-semibold text-gray-900 dark:text-white" numberOfLines={1}>{result.lab_name}</Text>
          
          {/* Basic lab result info - show all fields */}
          <InfoRow icon="person-outline" text={result.patient_name} />
          <InfoRow icon="card-outline" text={result.patient_id} />
          <InfoRow icon="key-outline" text={formatPassword()} />
          <InfoRow icon="calendar-outline" text={formatDate(result.result_date)} />

          {/* Always show website and phone fields */}
          <InfoRow icon="globe-outline" text={result.website} />
          <InfoRow icon="call-outline" text={result.phone_number1} />

        </View>

        {/* Action buttons section */}
        <View className="space-y- gap-3">
          {/* View Lab Result button - show if there's a PDF attachment */}
          {hasPdfAttachment && (
            <Button
              title={t('buttons.viewLabResult')}
              onPress={handleViewLabResult}
              variant="outline"
              icon="document-text-outline"
            />
          )}

          {/* AI Analysis button - only show if there's a PDF attachment */}
          {hasPdfAttachment && (
            <Button
              title={getAIButtonText()}
              onPress={handleAnalyzeWithAI}
              variant="outline"
              icon={getAIButtonIcon()}
              disabled={isAIButtonDisabled()}
            />
          )}

          {/* Check Results button - only show if there's a website but no PDF */}
          {result.website && !hasPdfAttachment && (
            <Button
              title={t('buttons.checkResults')}
              onPress={handleCheckResultsPress}
              variant="outline"
              icon="open-outline"
            />
          )}
        </View>
      </View>
    </Pressable>
  );
};

export default LabResultCard;