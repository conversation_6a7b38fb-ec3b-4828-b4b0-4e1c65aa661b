import React, { useState } from "react";
import {
  View,
  TextInput,
  Pressable,
  Platform,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from '@react-navigation/native';
import { useSafeAreaInsets } from "react-native-safe-area-context";

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

export function ChatInput({
  onSendMessage,
  disabled = false,
  placeholder = "Type your message...",
}: ChatInputProps) {
  const [message, setMessage] = useState("");
  const theme = useTheme();
  const insets = useSafeAreaInsets();

  const handleSend = () => {
    if (message.trim() && !disabled) {
      onSendMessage(message.trim());
      setMessage("");
    }
  };

  const canSend = message.trim().length > 0 && !disabled;

  return (
    <View
      className="px-4 py-3 bg-background dark:bg-neutral-900"
      style={{
        paddingBottom: Platform.OS === "ios" ? Math.max(insets.bottom, 12) : 12,
      }}
    >
      <View className="flex-row items-end space-x-3">
        {/* Text Input */}
        <View className="flex-1 min-h-[44px] max-h-[120px] bg-muted dark:bg-neutral-800 rounded-2xl px-4 py-3 justify-center border border-input dark:border-neutral-700">
          <TextInput
            value={message}
            onChangeText={setMessage}
            placeholder={placeholder}
            placeholderTextColor={theme.colors.text}
            multiline
            textAlignVertical="center"
            className="text-base text-foreground dark:text-neutral-100 min-h-[20px]"
            editable={!disabled}
            onSubmitEditing={handleSend}
            blurOnSubmit={false}
            style={{
              fontSize: 16,
              lineHeight: 20,
              paddingTop: Platform.OS === "ios" ? 0 : 2,
            }}
            returnKeyType="send"
            enablesReturnKeyAutomatically
          />
        </View>

        {/* Send Button */}
        <Pressable
          onPress={handleSend}
          disabled={!canSend}
          className={`w-11 h-11 rounded-full items-center justify-center ${
            canSend
              ? "bg-primary"
              : "bg-muted dark:bg-neutral-700"
          }`}
          style={{
            opacity: canSend ? 1 : 0.5,
          }}
        >
          <Ionicons
            name="send"
            size={20}
            color={canSend ? "white" : theme.colors.text}
          />
        </Pressable>
      </View>
    </View>
  );
} 