import React from "react";
import { View, Text } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from '@react-navigation/native';
import { useTranslation } from '@/hooks/useTranslation';

interface WelcomeMessageProps {
  chatType?: "general" | "lab_result" | "medication";
}

export function WelcomeMessage({ chatType = "general" }: WelcomeMessageProps) {
  const { t } = useTranslation();
  const theme = useTheme();
  
  const getWelcomeContent = () => {
    switch (chatType) {
      case "lab_result":
        return {
          icon: "document-text-outline" as const,
          iconColor: theme.colors.primary,
          title: t('aiChat.labResultAnalysis'),
          subtitle: t('aiChat.labResultAnalysisSubtitle'),
          description: t('aiChat.labResultAnalysisDescription'),
        };
      case "medication":
        return {
          icon: "medical-outline" as const,
          iconColor: theme.colors.primary,
          title: t('aiChat.medicationAnalysisTitle'), 
          subtitle: t('aiChat.medicationAnalysisSubtitle'),
          description: t('aiChat.medicationAnalysisDescription'),
        };
      default:
        return {
          icon: "chatbubble-ellipses-outline" as const,
          iconColor: theme.colors.primary,
          title: t('aiChat.generalChatTitle'),
          subtitle: t('aiChat.generalChatSubtitle'),
          description: t('aiChat.generalChatDescription'),
        };
    }
  };

  const content = getWelcomeContent();

  return (
    <View className="flex-1 justify-center items-center p-6 bg-background dark:bg-neutral-900">
      <View className="items-center max-w-sm">
        {/* Icon */}
        <View 
          className="w-20 h-20 rounded-full items-center justify-center mb-6 bg-muted dark:bg-neutral-800"
          style={{ backgroundColor: `${content.iconColor}20` }}
        >
          <Ionicons name={content.icon} size={40} color={content.iconColor} />
        </View>

        {/* Title */}
        <Text className="text-2xl font-bold text-center mb-4 text-foreground dark:text-neutral-100">
          {content.title}
        </Text>

        {/* Subtitle */}
        <Text className="text-lg font-medium text-center mb-4 text-foreground dark:text-neutral-200">
          {content.subtitle}
        </Text>

        {/* Description */}
        <Text className="text-base text-center text-muted-foreground dark:text-neutral-400 leading-6">
          {content.description}
        </Text>

        {/* Medical Disclaimer */}
        <View className="mt-8 p-4 bg-muted dark:bg-neutral-800 rounded-lg border border-border dark:border-neutral-700">
          <View className="flex-row items-start">
            <Ionicons name="warning-outline" size={20} color={theme.colors.notification} style={{marginTop: 2, marginRight: 12}} />
            <Text className="text-sm text-muted-foreground dark:text-neutral-400 flex-1 leading-5">
              {t('aiChat.medicalDisclaimer')}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
} 