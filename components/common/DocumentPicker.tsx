import React, { useState } from 'react';
import { View, Text, Pressable, ActivityIndicator } from 'react-native';
import * as DocumentPicker from 'expo-document-picker';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@react-navigation/native';
import { Button } from '../ui/Button';
import { useTranslation } from '@/hooks/useTranslation';

export interface DocumentResult {
  uri: string;
  name: string;
  size?: number;
  mimeType?: string;
}

interface DocumentPickerProps {
  onDocumentPicked: (document: DocumentResult) => void;
  onError?: (error: Error) => void;
  initialDocument?: DocumentResult;
  allowedTypes?: string[];
  buttonText?: string;
  onRemove?: () => void;
}

/**
 * A reusable component for picking document files
 */
const DocumentPickerComponent: React.FC<DocumentPickerProps> = ({
  onDocumentPicked,
  onError,
  initialDocument,
  allowedTypes = ['application/pdf', 'image/*'],
  buttonText,
  onRemove,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const [document, setDocument] = useState<DocumentResult | null>(initialDocument || null);
  const [isLoading, setIsLoading] = useState(false);

  // Handle document selection
  const handlePickDocument = async () => {
    setIsLoading(true);
    
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: allowedTypes,
        copyToCacheDirectory: true,
      });

      if (result.canceled === false && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        const documentResult: DocumentResult = {
          uri: asset.uri,
          name: asset.name,
          size: asset.size,
          mimeType: asset.mimeType,
        };
        
        setDocument(documentResult);
        onDocumentPicked(documentResult);
      }
    } catch (error) {
      console.error('Error picking document:', error);
      onError?.(error instanceof Error ? error : new Error('Failed to pick document'));
    } finally {
      setIsLoading(false);
    }
  };

  // Handle document removal
  const handleRemoveDocument = () => {
    setDocument(null);
    onRemove?.();
  };

  return (
    <View className="mb-4">
      {/* Button for selecting document */}
      <Button
        title={buttonText || t('misc.attachDocument')}
        onPress={handlePickDocument}
        variant="outline"
        icon="document-attach"
        fullWidth
        disabled={isLoading}
      />

      {/* Loading indicator */}
      {isLoading && (
        <View className="items-center py-3 mt-3 bg-primary/10 dark:bg-primary/20 rounded-lg">
          <ActivityIndicator size="small" className="text-primary" />
          <Text className="mt-2 text-sm text-primary dark:text-blue-300">{t('misc.selectingDocument')}</Text>
        </View>
      )}

      {/* Selected document info */}
      {document && (
        <View className="p-3 mt-3 border border-border dark:border-neutral-700 rounded-lg bg-background dark:bg-neutral-800">
          <View className="flex-row justify-between items-center">
            <View className="flex-row items-center flex-1 mr-2">
              <Ionicons 
                name={document.mimeType?.includes('pdf') ? 'document-text-outline' : document.mimeType?.includes('image') ? 'image-outline' : 'document-outline'} 
                size={20} 
                color={theme.colors.text} 
              />
              <Text className="ml-2 text-foreground dark:text-neutral-200 font-medium" numberOfLines={1}>
                {document.name}
              </Text>
            </View>
            {onRemove && (
              <Pressable
                onPress={handleRemoveDocument}
                className="p-1"
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons name="close-circle-outline" size={24} color={theme.colors.notification} />
              </Pressable>
            )}
          </View>
          
          {/* File size if available */}
          {document.size && (
            <Text className="text-xs text-muted-foreground dark:text-neutral-500 mt-1 ml-7">
              {(document.size / 1024).toFixed(1)} KB
            </Text>
          )}
        </View>
      )}
    </View>
  );
};

export default DocumentPickerComponent; 