import React, { useEffect } from 'react';
import { View, Text, Alert, Pressable, ActivityIndicator, Linking } from 'react-native';
import { differenceInDays, format } from 'date-fns';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@react-navigation/native';
import { router } from 'expo-router';
import { Image } from 'expo-image';
import { MedicineWithRelations } from '@/hooks/entities/useMedicines';
import { useSupabaseStorageUrl } from '@/hooks/useSupabaseStorageUrl';
import { Button } from '../ui/Button';
import { useAnalyses } from '@/hooks/entities/useAnalyses';
import { useChatSessions } from '@/hooks/entities/useChatSessions';
import { useTranslation } from '@/hooks/useTranslation';
import { useCheckMedicinePrices } from '@/hooks/useCheckMedicinePrices';
import { useRateLimitStore } from '@/store/rateLimitStore';

interface MedicineCardProps {
  medicine: MedicineWithRelations;
  onPress?: () => void;
}

const formatDate = (dateString: string | null): string => {
  if (!dateString) return 'N/A';
  try {
    return format(new Date(dateString), 'dd/MM/yy');
  } catch {
    return 'Invalid date';
  }
};

const InfoRow: React.FC<{ icon: keyof typeof Ionicons.glyphMap; text: string | null | undefined; color?: string; numberOfLines?: number; }> = ({ icon, text, color = 'text-gray-600 dark:text-gray-400', numberOfLines = 1 }) => {
  const theme = useTheme();
  return (
    <View className="flex-row items-start mt-1">
      <Ionicons name={icon} size={14} color={theme.colors.text} style={{marginTop: 2}} />
      <Text className={`text-xs ml-1.5 ${color} flex-1`} numberOfLines={numberOfLines}>{text || 'N/A'}</Text>
    </View>
  );
};

const MedicineCard: React.FC<MedicineCardProps> = ({ medicine, onPress }) => {
  const { t } = useTranslation();
  const { useFetchMedicationAnalysis, useCreateMedicationAnalysis } = useAnalyses();
  const { useCreateChatSession } = useChatSessions();
  const { checkPrices, isPending: isPriceChecking, timeUntilNextRequest, isRateLimited } = useCheckMedicinePrices();
  const { currentCountdown, startCountdown, stopCountdown } = useRateLimitStore();

  const { data: analysis, isLoading: isCheckingAnalysis } = useFetchMedicationAnalysis(medicine.id);
  const createAnalysisMutation = useCreateMedicationAnalysis();
  const createSessionMutation = useCreateChatSession();

  const { url: imageUrl, isLoading: isLoadingUrl } = useSupabaseStorageUrl({
    bucketId: medicine.image_bucket_id || 'user-meds',
    path: medicine.image_path,
  });

  // Start countdown when rate limited
  useEffect(() => {
    if (isRateLimited && timeUntilNextRequest > 0) {
      startCountdown();
    } else {
      stopCountdown();
    }
  }, [isRateLimited, timeUntilNextRequest, startCountdown, stopCountdown]);

  const getExpirationStatus = () => {
    if (!medicine.expiration_date) return null;
    const today = new Date();
    const expirationDate = new Date(medicine.expiration_date);
    const daysUntilExpiration = differenceInDays(expirationDate, today);

    if (daysUntilExpiration < 0) return { text: `Expired ${Math.abs(daysUntilExpiration)} days ago`, color: 'text-red-500', icon: 'warning' as const };
    if (daysUntilExpiration === 0) return { text: 'Expires today', color: 'text-red-500', icon: 'warning' as const };
    if (daysUntilExpiration <= 30) return { text: `Expires in ${daysUntilExpiration} days`, color: 'text-orange-500', icon: 'time' as const };
    return null;
  };

  const expirationStatus = getExpirationStatus();

  const handleAnalyzeWithAI = async () => {
    if (createAnalysisMutation.isPending || createSessionMutation.isPending) return;
    if (analysis?.session_id) {
      router.push(`/(ai-chat)/${analysis.session_id}` as any);
      return;
    }
    try {
      const session = await createSessionMutation.mutateAsync({ type: 'medication', contextId: medicine.id, title: `${medicine.name} Analysis` });
      router.push(`/(ai-chat)/${session.id}` as any);
    } catch (error) {
      console.error("Failed to create session:", error);
      Alert.alert(t('common.error'), t('aiChat.failedToOpenChat'));
    }
  };

  const getAIButtonText = () => {
    if (isCheckingAnalysis) return t('status.loading');
    if (createAnalysisMutation.isPending || createSessionMutation.isPending) return t('status.analyzing');
    if (analysis?.session_id) return t('aiChat.continueChat');
    return t('buttons.analyzeWithAI');
  };

  const getAIButtonIcon = () => {
    if (isCheckingAnalysis || createAnalysisMutation.isPending || createSessionMutation.isPending) return "hourglass-outline";
    if (analysis?.session_id) return "chatbubble-outline";
    return "sparkles-outline";
  };

  const isAIButtonDisabled = () => isCheckingAnalysis || createAnalysisMutation.isPending || createSessionMutation.isPending;

  const handleCheckPrices = async () => {
    if (isPriceChecking || isRateLimited) return;
    
    try {
      const results = await checkPrices({ medicineName: medicine.name, medicineId: medicine.id });
      
      if (results.results.length > 0) {
        // Navigate to price results modal with fresh data
        router.push({
          pathname: '/(protected)/modals/medicine-prices' as const,
          params: { 
            medicineName: medicine.name,
            medicineId: medicine.id,
            priceData: JSON.stringify(results)
          }
        } as any);
      } else {
        Alert.alert(t('common.info'), t('priceCheck.noResultsFound'));
      }
    } catch (error) {
      console.error('Failed to check prices:', error);
      
      // Handle location permission errors specifically
      if (error instanceof Error) {
        // Handle rate limit errors
        if (error.message.startsWith('RATE_LIMIT_EXCEEDED:')) {
          const remainingTime = error.message.split(':')[1];
          Alert.alert(
            t('common.error'),
            t('priceCheck.rateLimitExceeded', { time: remainingTime }),
            [{ text: t('common.ok'), style: 'cancel' }]
          );
          return;
        }
        
        switch (error.message) {
          case 'LOCATION_PERMISSION_PERMANENTLY_DENIED':
            Alert.alert(
              t('common.error'),
              t('priceCheck.locationPermanentlyDenied'),
              [
                {
                  text: t('common.cancel'),
                  style: 'cancel'
                },
                {
                  text: t('priceCheck.openSettings'),
                  onPress: () => Linking.openSettings()
                }
              ]
            );
            break;
            
          case 'LOCATION_SERVICES_DISABLED':
            Alert.alert(
              t('common.error'),
              t('priceCheck.locationServicesDisabled'),
              [
                {
                  text: t('common.cancel'),
                  style: 'cancel'
                },
                {
                  text: t('priceCheck.openSettings'),
                  onPress: () => Linking.openSettings()
                }
              ]
            );
            break;
            
          case 'LOCATION_WEB_NOT_SUPPORTED':
            Alert.alert(
              t('common.error'),
              t('priceCheck.locationWebNotSupported'),
              [
                {
                  text: t('common.ok'),
                  style: 'cancel'
                }
              ]
            );
            break;
            
          case 'LOCATION_PERMISSION_DENIED':
            Alert.alert(
              t('common.error'),
              t('priceCheck.locationPermissionDenied'),
              [
                {
                  text: t('common.cancel'),
                  style: 'cancel'
                },
                {
                  text: t('priceCheck.deleteAppReinstall'),
                  onPress: () => Alert.alert(
                    t('priceCheck.resetPermissions'),
                    t('priceCheck.resetPermissionsInstructions'),
                    [{ text: t('common.ok') }]
                  )
                },
                {
                  text: t('common.tryAgain'),
                  onPress: () => handleCheckPrices()
                }
              ]
            );
            break;
            
          default:
            Alert.alert(t('common.error'), t('priceCheck.failedToCheckPrices'));
        }
      } else {
        Alert.alert(t('common.error'), t('priceCheck.failedToCheckPrices'));
      }
    }
  };

  const formatDosage = () => medicine.dosage_amount && medicine.dosage_unit ? `${medicine.dosage_amount} ${medicine.dosage_unit}` : "Not specified";
  const formatFrequency = () => medicine.frequency_amount && medicine.frequency_unit ? `${medicine.frequency_amount} ${medicine.frequency_unit}` : "Not specified";
  const formatDuration = () => {
    if (medicine.duration_unit === 'Ongoing' || medicine.duration_unit === 'As needed') return medicine.duration_unit;
    return medicine.duration_amount && medicine.duration_unit ? `${medicine.duration_amount} ${medicine.duration_unit}` : "Not specified";
  };

  const getPriceCheckButtonText = () => {
    if (isPriceChecking) return t('status.checking');
    if (isRateLimited && currentCountdown > 0) return t('priceCheck.waitTime', { time: currentCountdown });
    return t('buttons.checkPrices');
  };

  const getPriceCheckButtonIcon = () => {
    if (isPriceChecking) return "hourglass-outline";
    if (isRateLimited && currentCountdown > 0) return "time-outline";
    return "pricetag-outline";
  };

  return (
    <Pressable onPress={onPress} className="bg-white dark:bg-neutral-800 rounded-xl shadow-sm border border-gray-200 dark:border-neutral-700 flex-1 m-1 overflow-hidden flex flex-col">
      {/* Image Container */}
      <View className="relative" style={{ aspectRatio: 16 / 9 }}>
        {isLoadingUrl ? (
          <View className="h-full bg-gray-100 dark:bg-neutral-700 justify-center items-center">
            <ActivityIndicator />
          </View>
        ) : imageUrl ? (
          <Image source={{ uri: imageUrl }} style={{ height: '100%', width: '100%' }} contentFit="cover" />
        ) : (
          <Image 
            source={require('../../assets/images/defaults/meds-thumb.jpeg')} 
            style={{ height: '100%', width: '100%' }} 
            contentFit="cover" 
          />
        )}

        {/* Overlays */}
        <View className="absolute top-0 left-0 right-0 p-2 flex-row justify-between">
          <View className="flex-row items-center bg-black/50 px-2 py-1 rounded-full">
            <Ionicons name="calendar-outline" size={12} color="white" />
            <Text className="text-white text-xs font-medium ml-1">Opened: {formatDate(medicine.opened_on_date)}</Text>
          </View>
          <View className="bg-green-500/70 px-2 py-1 rounded-full">
            <Text className="text-white text-xs font-medium">{medicine.price ? `${medicine.price.toFixed(2)}` : '$0.00'}</Text>
          </View>
        </View>
        <View className="absolute bottom-0 left-0 right-0 p-2 flex-row justify-between">
          <View className="flex-row items-center bg-black/50 px-2 py-1 rounded-full">
            <Ionicons name="calendar-outline" size={12} color="white" />
            <Text className="text-white text-xs font-medium ml-1">Expires: {formatDate(medicine.expiration_date)}</Text>
          </View>
          <View className="flex-row items-center bg-black/50 px-2 py-1 rounded-full">
            <Ionicons name="person-outline" size={12} color="white" />
            <Text className="text-white text-xs font-medium ml-1" numberOfLines={1}>{medicine.patients?.name || 'N/A'}</Text>
          </View>
        </View>
      </View>

      {/* Content */}
      <View className="p-3 flex-1 flex flex-col justify-between">
        <View className="pb-3">
          <Text className="text-base font-semibold text-gray-900 dark:text-white" numberOfLines={1}>{medicine.name}</Text>
          
          <View style={{ minHeight: 20 }}>
            {expirationStatus && <InfoRow icon={expirationStatus.icon} text={expirationStatus.text} color={expirationStatus.color} />}
          </View>
          
          <InfoRow icon="eyedrop-outline" text={formatDosage()} />
          <InfoRow icon="time-outline" text={formatFrequency()} />
          <InfoRow icon="hourglass-outline" text={formatDuration()} />

          <View className="mt-2 pt-2 border-t border-gray-200 dark:border-neutral-700">
            <InfoRow icon="document-text-outline" text={medicine.notes} numberOfLines={2} />
            <InfoRow icon="information-circle-outline" text={medicine.description} numberOfLines={2} />
          </View>
        </View>

        <View className="space-y-2">
          <Button
            title={getAIButtonText()}
            onPress={handleAnalyzeWithAI}
            variant="outline"
            icon={getAIButtonIcon()}
            disabled={isAIButtonDisabled()}
          />
          
          <Button
            title={getPriceCheckButtonText()}
            onPress={handleCheckPrices}
            variant="outline"
            icon={getPriceCheckButtonIcon()}
            disabled={isPriceChecking || isRateLimited}
          />
        </View>
      </View>
    </Pressable>
  );
};

export default MedicineCard;

 