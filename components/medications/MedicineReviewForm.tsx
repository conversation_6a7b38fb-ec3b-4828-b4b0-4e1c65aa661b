import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, Alert, SafeAreaView } from 'react-native';
import { useForm, Controller, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

import { Button } from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import ControlledInput from '@/components/ControlledInput';
import RouterSelectInput, { RouterSelectOption } from '@/components/common/RouterSelectInput';
import { useOcrStore } from '@/store/ocrStore';
import { useMedicines } from '@/hooks/entities/useMedicines';
import { usePatients, Patient } from '@/hooks/entities/usePatients';
import { useAppAlerts } from '@/hooks/useAppAlerts';
import { useTranslation } from '@/hooks/useTranslation';
import { useTheme } from '@react-navigation/native';

// Schema for the medicine review form
const medicineReviewSchema = z.object({
  medicines: z.array(z.object({
    name: z.string().min(1, 'Medicine name is required'),
    price: z.coerce.number().nonnegative().optional(),
    description: z.string().optional(),
    patient_id: z.string().uuid().optional(),
    notes: z.string().optional(),
  }))
});

type MedicineReviewFormData = z.infer<typeof medicineReviewSchema>;

interface MedicineReviewFormProps {
  onComplete: () => void;
}

export default function MedicineReviewForm({ onComplete }: MedicineReviewFormProps) {
  const router = useRouter();
  const { t } = useTranslation();
  const theme = useTheme();
  const { showSuccess, showError } = useAppAlerts();
  const [isSaving, setIsSaving] = useState(false);
  
  const { 
    capturedImageUri, 
    extractedMedicines, 
    resetOcrState 
  } = useOcrStore();
  
  const { useBulkCreateFromReceipt } = useMedicines();
  const bulkCreateMutation = useBulkCreateFromReceipt();
  
  const { useFetchPatients } = usePatients();
  const { data: patientsData, isLoading: isLoadingPatients } = useFetchPatients();

  const { control, handleSubmit, formState: { errors } } = useForm<MedicineReviewFormData>({
    resolver: zodResolver(medicineReviewSchema),
    defaultValues: {
      medicines: extractedMedicines.map(med => ({
        name: med.name,
        price: med.price || undefined,
        description: med.description || undefined,
        patient_id: undefined,
        notes: undefined,
      }))
    }
  });

  const { fields, remove } = useFieldArray({
    control,
    name: 'medicines'
  });

  const patientOptions = React.useMemo((): RouterSelectOption[] => {
    return patientsData?.map((patient: Patient): RouterSelectOption => ({
      id: patient.id,
      label: patient.name,
      subtitle: patient.bloodtype ? `${t('misc.bloodType')}: ${patient.bloodtype}` : undefined,
      itemType: 'patient',
      data: patient,
    })) || [];
  }, [patientsData, t]);

  // Update form when extracted medicines change
  useEffect(() => {
    if (extractedMedicines.length === 0) {
      // If no medicines extracted, go back
      router.back();
    }
  }, [extractedMedicines, router]);

  const onSubmit = async (data: MedicineReviewFormData) => {
    if (data.medicines.length === 0) {
      showError(t('ocr.noMedicinesFound'));
      return;
    }

    // Check for form validation errors
    const hasFormErrors = Object.keys(errors).length > 0;
    if (hasFormErrors) {
      showError(t('validation.formHasErrors'));
      return;
    }

    setIsSaving(true);
    try {
      await bulkCreateMutation.mutateAsync({
        medicines: data.medicines,
        receiptImageUri: capturedImageUri || undefined
      });
      
      showSuccess(t('medicines.addedSuccessfully'));
      resetOcrState();
      onComplete();
    } catch (error) {
      showError(error instanceof Error ? error.message : t('medicines.saveFailed', {action: 'save'}));
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    Alert.alert(
      t('common.cancel'),
      t('dialogs.unsavedChangesMessage'),
      [
        { text: t('common.no'), style: 'cancel' },
        { 
          text: t('common.yes'), 
          style: 'destructive' as any,
          onPress: () => {
            resetOcrState();
            onComplete();
          }
        }
      ]
    );
  };

  const removeMedicine = (index: number) => {
    Alert.alert(
      t('common.remove'),
      t('alerts.confirmDelete'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        { 
          text: t('common.remove'), 
          style: 'destructive' as any,
          onPress: () => remove(index)
        }
      ]
    );
  };

  if (extractedMedicines.length === 0) {
    return (
      <View className="flex-1 justify-center items-center p-6 bg-background dark:bg-neutral-900">
        <Ionicons name="medical-outline" size={64} color={theme.colors.text} style={{ marginBottom: 16 }} />
        <Text className="text-lg font-semibold text-center mb-2 text-foreground dark:text-neutral-100">{t('ocr.noMedicinesFound')}</Text>
        <Text className="text-muted-foreground dark:text-neutral-400 text-center mb-6">
          {t('ocr.tryAgain')}
        </Text>
        <Button
          title={t('common.tryAgain')}
          onPress={() => router.back()}
          variant="primary"
        />
      </View>
    );
  }

  return (
    <View className="flex-1 bg-background dark:bg-neutral-900">
      <SafeAreaView className="flex-1 bg-background dark:bg-neutral-900">
      <ScrollView className="flex-1 p-4" keyboardShouldPersistTaps="handled">
        {/* Receipt Image Preview */}
        {capturedImageUri && (
          <View className="mb-6">
            <Card title={t('fields.receiptImage')}>
              <View className="flex-row items-center mb-3">
                <Ionicons name="receipt-outline" size={20} color={theme.colors.text} style={{ marginRight: 8 }} />
              </View>
              <Image
                source={{ uri: capturedImageUri }}
                style={{ width: '100%', height: 200, borderRadius: 8 }}
                contentFit="contain"
              />
            </Card>
          </View>
        )}

        {/* Instructions */}
        <View className="mb-6">
                      <Card title={t('medicines.reviewExtractedMedicines')}>
            <Text className="text-muted-foreground">
              {t('ocr.reviewMedicines')}
            </Text>
          </Card>
        </View>

        {/* Medicine List */}
        {fields.map((field, index) => (
          <View key={field.id} className="mb-4">
            <Card title={`${t('navigation.medicines')} ${index + 1}`}>
              <View className="space-y-4">
                <View className="flex-row justify-end">
                  <Button
                    title={t('common.remove')}
                    onPress={() => removeMedicine(index)}
                    variant="danger"
                  />
                </View>

                <ControlledInput
                  control={control}
                  name={`medicines.${index}.name`}
                  label={t('fields.name')}
                  placeholder={t('placeholders.medicineName')}
                  leftIcon="medkit"
                />

                <ControlledInput
                  control={control}
                  name={`medicines.${index}.price`}
                  label={t('common.price')}
                  placeholder={t('placeholders.price')}
                  keyboardType="numeric"
                  leftIcon="cash-outline"
                />

                <ControlledInput
                  control={control}
                  name={`medicines.${index}.description`}
                  label={t('common.description')}
                  placeholder={t('placeholders.description')}
                  leftIcon="document-text-outline"
                  multiline
                />

                <Controller
                  control={control}
                  name={`medicines.${index}.patient_id`}
                  render={({ field: { onChange, value } }) => (
                    <RouterSelectInput
                      label={t('fields.patient')}
                      options={patientOptions}
                      selectedOption={patientOptions.find(option => option.id === value)}
                      onSelect={(option) => onChange(option?.id ?? undefined)}
                      isLoading={isLoadingPatients}
                      modalTitle={t('placeholders.selectPatientOptional')}
                      placeholder={t('placeholders.selectPatientOptional')}
                      showCreateNewButton={true}
                      onCreateNew={() => router.push('/modals/modal-patient')}
                      createText={t('misc.addNewPatient')}
                      modalPlaceholder={t('placeholders.searchPatients')}
                      modalEmptyResultsMessage={
                        patientsData?.length === 0
                          ? t('patients.noPatientsMessage')
                          : t('common.noResults')
                      }
                      clearable={true}
                    />
                  )}
                />

                <ControlledInput
                  control={control}
                  name={`medicines.${index}.notes`}
                  label={t('fields.notes')}
                  placeholder={t('placeholders.enterAnyNotes')}
                  leftIcon="create-outline"
                  multiline
                />
              </View>
            </Card>
          </View>
        ))}

        {/* Action Buttons */}
        <View className="flex-row gap-4 mt-6">
          <View className="flex-1">
            <Button
              title="Cancel"
              onPress={handleCancel}
              variant="outline"
              disabled={isSaving || bulkCreateMutation.isPending}
            />
          </View>
          <View className="flex-1">
            <Button
              title={isSaving ? 'Saving...' : `Save ${fields.length} Medicine(s)`}
              onPress={handleSubmit(onSubmit)}
              variant="primary"
              isLoading={isSaving || bulkCreateMutation.isPending}
              icon="checkmark-outline"
            />
          </View>
        </View>
      </ScrollView>
      </SafeAreaView>
    </View>
  );
} 