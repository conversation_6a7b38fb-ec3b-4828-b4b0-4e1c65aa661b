import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Modal, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@react-navigation/native';
import { DaysOfWeek, WorkingHours, defaultWorkingHours } from '@/schema/working-hours';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useTranslation } from '@/hooks/useTranslation';

interface WorkingHoursSelectorProps {
  value: WorkingHours | null | undefined;
  onChange: (hours: WorkingHours) => void;
}

const WorkingHoursSelector = ({ value, onChange }: WorkingHoursSelectorProps) => {
  const { colors, dark: isDark } = useTheme();
  const { t } = useTranslation();

  // Internal state for working hours
  const [internalWorkingHours, setInternalWorkingHours] = useState<WorkingHours>(() => {
    // Initialize state based on initial value or default
    let initialHours: WorkingHours;
    if (!value) {
      initialHours = { ...defaultWorkingHours };
    } else {
      initialHours = { ...value };
      // Ensure all days are present
      DaysOfWeek.forEach(day => {
        if (!(day in initialHours)) {
          initialHours[day] = null;
        }
      });
    }
    return initialHours;
  });

  const [expandedDay, setExpandedDay] = useState<string | null>(null);
  const [showPicker, setShowPicker] = useState(false);
  const [currentPicker, setCurrentPicker] = useState<{
    day: string;
    slotIndex: number;
    field: 'start' | 'end';
  } | null>(null);

  // Effect to sync internal state with prop changes
  useEffect(() => {
    let updatedHours: WorkingHours;
    if (!value) {
      updatedHours = { ...defaultWorkingHours };
    } else {
      updatedHours = { ...value };
      // Ensure all days are present
      DaysOfWeek.forEach(day => {
        if (!(day in updatedHours)) {
          updatedHours[day] = null;
        }
      });
    }
    // Use functional update to avoid needing internalWorkingHours in dependency array
    setInternalWorkingHours(current => {
      // Avoid unnecessary state updates if objects are deeply equal
      if (JSON.stringify(updatedHours) !== JSON.stringify(current)) {
        return updatedHours;
      }
      return current;
    });
  }, [value]); // Only depend on value to prevent infinite loops

  // Convert Date to time string (HH:MM)
  const dateToTimeString = (date: Date) => {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  };

  // Convert time string (HH:MM) to Date
  const timeStringToDate = (timeString: string) => {
    const [hours, minutes] = timeString.split(':').map(Number);
    const date = new Date();
    date.setHours(hours);
    date.setMinutes(minutes);
    return date;
  };

  // Helper function to update a time slot
  const updateTimeSlot = (day: string, slotIndex: number, field: 'start' | 'end', timeString: string) => {
    // Create a new working hours object based on INTERNAL state
    const newWorkingHours = { ...internalWorkingHours };

    // Initialize the day's slots if they don't exist
    if (!newWorkingHours[day]) {
      newWorkingHours[day] = [];
    }

    // Initialize the slot if it doesn't exist
    if (!newWorkingHours[day][slotIndex]) {
      newWorkingHours[day][slotIndex] = { start: '09:00', end: '17:00' };
    }

    // Update the specific field
    newWorkingHours[day][slotIndex] = {
      ...newWorkingHours[day][slotIndex],
      [field]: timeString
    };

    setInternalWorkingHours(newWorkingHours);
    onChange(newWorkingHours);
  };

  // Handle time change from picker
  const handleTimeChange = (_event: any, selectedDate?: Date) => {
    setShowPicker(false);

    if (selectedDate && currentPicker) {
      const { day, slotIndex, field } = currentPicker;
      const timeString = dateToTimeString(selectedDate);
      updateTimeSlot(day, slotIndex, field, timeString);
    }
  };

  // Add a new time slot for a day (max 2 slots per day)
  const addTimeSlot = (day: string) => {
    const newWorkingHours = { ...internalWorkingHours }; // Use internal state
    if (!newWorkingHours[day]) {
      newWorkingHours[day] = [];
    }

    // Limit to maximum 2 slots per day
    if (newWorkingHours[day].length < 2) {
      newWorkingHours[day].push({ start: '09:00', end: '17:00' });
      setInternalWorkingHours(newWorkingHours);
      onChange(newWorkingHours);
    }
  };

  // Remove a time slot for a day
  const removeTimeSlot = (day: string, index: number) => {
    const newWorkingHours = { ...internalWorkingHours }; // Use internal state
    if (newWorkingHours[day] && newWorkingHours[day].length > index) {
      newWorkingHours[day].splice(index, 1);
      if (newWorkingHours[day].length === 0) {
          newWorkingHours[day] = null; // Ensure day is null if no slots remain
      }
      setInternalWorkingHours(newWorkingHours);
      onChange(newWorkingHours);
    }
  };

  // Toggle day expansion
  const toggleDay = (day: string) => {
    setExpandedDay(expandedDay === day ? null : day);
  };

  // Show time picker
  const showTimePicker = (day: string, slotIndex: number, field: 'start' | 'end') => {
    setCurrentPicker({ day, slotIndex, field });
    setShowPicker(true);
  };

  // Format day name for display
  const formatDayName = (day: string) => {
    return day.charAt(0).toUpperCase() + day.slice(1);
  };

  // Render time slots for a day
  const renderTimeSlots = (day: string) => {
    // Use internal state for rendering
    const slots = internalWorkingHours[day] || [];

    if (slots.length === 0) {
      return (
        <View className="py-2 px-4">
          <Text className="text-neutral-500 dark:text-neutral-400 italic">{t('misc.noWorkingHoursSet')}</Text>
          <TouchableOpacity
            onPress={() => addTimeSlot(day)}
            className="flex-row items-center mt-2"
          >
            <Ionicons name="add-circle-outline" size={18} color={theme.colors.primary} />
            <Text className="ml-2 text-primary">{t('misc.addHours')}</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View className="py-2">
        {slots.map((slot, index) => (
          <View key={index} className="flex-row items-center justify-between px-4 py-2">
            <View className="flex-row items-center">
              <TouchableOpacity
                onPress={() => showTimePicker(day, index, 'start')}
                className="bg-neutral-100 dark:bg-neutral-800 px-3 py-1 rounded-lg mr-2"
              >
                <Text className="text-neutral-800 dark:text-neutral-200">
                  {slot.start}
                </Text>
              </TouchableOpacity>
              <Text className="text-neutral-500 dark:text-neutral-400 mx-1">{t('misc.to')}</Text>
              <TouchableOpacity
                onPress={() => showTimePicker(day, index, 'end')}
                className="bg-neutral-100 dark:bg-neutral-800 px-3 py-1 rounded-lg ml-2"
              >
                <Text className="text-neutral-800 dark:text-neutral-200">
                  {slot.end}
                </Text>
              </TouchableOpacity>
            </View>
            <TouchableOpacity
              onPress={() => removeTimeSlot(day, index)}
              className="p-2"
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Ionicons name="close-circle-outline" size={20} color={theme.colors.notification} />
            </TouchableOpacity>
          </View>
        ))}
        {slots.length < 2 && (
          <TouchableOpacity
            onPress={() => addTimeSlot(day)}
            className="flex-row items-center mt-1 px-4"
          >
            <Ionicons name="add-circle-outline" size={18} color={theme.colors.primary} />
            <Text className="ml-2 text-primary">{t('misc.addAnotherTimeSlot')}</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  return (
    <View className="mb-4">
      <Text className="text-lg font-semibold mb-3 text-neutral-800 dark:text-neutral-200">
        {t('misc.workingHours')}
      </Text>

      <View className="border border-neutral-300 dark:border-neutral-700 rounded-lg overflow-hidden">
        {DaysOfWeek.map((day) => (
          <View key={day} className="border-b border-neutral-300 dark:border-neutral-700 last:border-b-0">
            <TouchableOpacity
              onPress={() => toggleDay(day)}
              className="flex-row justify-between items-center px-4 py-3 bg-neutral-50 dark:bg-neutral-900"
            >
              <Text className="font-medium text-neutral-800 dark:text-neutral-200">
                {formatDayName(day)}
              </Text>
              <View className="flex-row items-center">
                {internalWorkingHours[day] && internalWorkingHours[day]?.length > 0 ? (
                  <Text className="text-neutral-500 dark:text-neutral-400 mr-2 text-sm">
                    {internalWorkingHours[day]?.map((slot, index) =>
                      `${slot.start}-${slot.end}${index < (internalWorkingHours[day]?.length || 0) - 1 ? ', ' : ''}`
                    )}
                  </Text>
                ) : (
                  <Text className="text-neutral-500 dark:text-neutral-400 mr-2 text-sm italic">
                    {t('misc.notSet')}
                  </Text>
                )}
                <Ionicons
                  name={expandedDay === day ? 'chevron-up' : 'chevron-down'}
                  size={20}
                  color={colors.text}
                />
              </View>
            </TouchableOpacity>

            {expandedDay === day && renderTimeSlots(day)}
          </View>
        ))}
      </View>

      {/* Time picker modal for iOS */}
      {Platform.OS === 'ios' && (
        <Modal
          visible={showPicker}
          transparent={true}
          animationType="slide"
        >
          <View style={{
            flex: 1,
            justifyContent: 'flex-end',
          }}>
            <View style={{
              backgroundColor: isDark ? '#1f2937' : 'white',
              borderTopLeftRadius: 10,
              borderTopRightRadius: 10,
              padding: 20
            }}>
              <View style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                marginBottom: 15
              }}>
                <TouchableOpacity onPress={() => setShowPicker(false)}>
                  <Text style={{ color: colors.primary, fontSize: 16 }}>{t('common.cancel')}</Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => {
                  if (currentPicker) {
                    // Use current value when done is pressed
                    const timeString = internalWorkingHours[currentPicker.day]?.[currentPicker.slotIndex]?.[currentPicker.field] || '09:00';
                    const date = timeStringToDate(timeString);
                    handleTimeChange(null, date);
                  }
                  setShowPicker(false);
                }}>
                  <Text style={{ color: colors.primary, fontSize: 16, fontWeight: 'bold' }}>{t('misc.done')}</Text>
                </TouchableOpacity>
              </View>

              {currentPicker && (
                <DateTimePicker
                  value={timeStringToDate(
                    internalWorkingHours[currentPicker.day]?.[currentPicker.slotIndex]?.[currentPicker.field] || '09:00'
                  )}
                  mode="time"
                  is24Hour={true}
                  display="spinner"
                  onChange={(_event, date) => {
                    if (date && currentPicker) {
                      const { day, slotIndex, field } = currentPicker;
                      const timeString = dateToTimeString(date);
                      updateTimeSlot(day, slotIndex, field, timeString);
                    }
                  }}
                />
              )}
            </View>
          </View>
        </Modal>
      )}

      {/* Android uses the default picker which appears as a dialog */}
      {Platform.OS === 'android' && showPicker && currentPicker && (
        <DateTimePicker
          value={timeStringToDate(
            internalWorkingHours[currentPicker.day]?.[currentPicker.slotIndex]?.[currentPicker.field] || '09:00'
          )}
          mode="time"
          is24Hour={true}
          display="default"
          onChange={handleTimeChange}
        />
      )}
    </View>
  );
};

export default WorkingHoursSelector;