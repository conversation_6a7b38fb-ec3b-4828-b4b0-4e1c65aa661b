import React, { useState, useEffect } from 'react';
import { View, Text, Switch, Alert, Platform, Modal, useColorScheme } from 'react-native';
import { Button } from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { useMedicineExpirationPushNotifications, MedicineExpirationNotificationConfig } from '@/hooks/useMedicineExpirationPushNotifications';
import DateTimePicker, { DateTimePickerAndroid } from '@react-native-community/datetimepicker';
import { useTranslation } from '@/hooks/useTranslation';

export default function MedicineNotificationSettings() {
  const { t } = useTranslation();
  const {
    getConfig,
    saveConfig,
    requestPermissions,
    getPendingNotifications,
    getScheduledNotificationCount,
  } = useMedicineExpirationPushNotifications();

  const [config, setConfig] = useState<MedicineExpirationNotificationConfig | null>(null);
  const [pendingCount, setPendingCount] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [showIOSTimePicker, setShowIOSTimePicker] = useState(false);
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Load configuration on mount
  useEffect(() => {
    const initializeNotifications = async () => {
      setLoading(true);
      try {
        // Load config
        const savedConfig = await getConfig();
        setConfig(savedConfig);
        
        // Load pending notification count
        const count = await getScheduledNotificationCount();
        setPendingCount(count);
        
        // Check pending notifications and display count
        const pending = await getPendingNotifications();
        console.log(`Found ${pending.length} pending notifications`);
      } catch (error) {
        console.error('Error loading notification config:', error);
      } finally {
        setLoading(false);
      }
    };
    
    initializeNotifications();
  }, [getConfig, getScheduledNotificationCount, getPendingNotifications]);

  const updateConfig = async (updates: Partial<MedicineExpirationNotificationConfig>) => {
    if (!config) return;

    const newConfig = { ...config, ...updates };
    setConfig(newConfig);

    try {
      await saveConfig(newConfig);
      // Refresh pending count
      const count = await getScheduledNotificationCount();
      setPendingCount(count);
    } catch (error) {
      console.error('Error saving notification config:', error);
      Alert.alert(t('common.error'), t('alerts.failedToSaveSettings'));
      // Revert config on error
      setConfig(config);
    }
  };

  const handleEnableToggle = async (enabled: boolean) => {
    if (enabled) {
      // Request permissions when enabling
      const hasPermission = await requestPermissions();
      if (!hasPermission) {
        return; // Don't enable if permissions not granted
      }
    }
    updateConfig({ enabled });
  };

  const showTimePicker = () => {
    if (!config) return;

    const [hours, minutes] = config.notificationTime.split(':').map(Number);
    const currentTime = new Date();
    currentTime.setHours(hours, minutes, 0, 0);

    if (Platform.OS === 'android') {
      DateTimePickerAndroid.open({
        value: currentTime,
        onChange: (event, selectedTime) => {
          if (event.type === 'set' && selectedTime) {
            const newTime = `${selectedTime.getHours().toString().padStart(2, '0')}:${selectedTime.getMinutes().toString().padStart(2, '0')}`;
            updateConfig({ notificationTime: newTime });
          }
        },
        mode: 'time',
        is24Hour: true,
      });
    } else {
      // iOS - show modal with DateTimePicker
      setShowIOSTimePicker(true);
    }
  };

  const handleIOSTimeChange = (_event: any, selectedTime?: Date) => {
    if (selectedTime && config) {
      const newTime = `${selectedTime.getHours().toString().padStart(2, '0')}:${selectedTime.getMinutes().toString().padStart(2, '0')}`;
      updateConfig({ notificationTime: newTime });
    }
  };

  const testNotifications = async () => {
    try {
      const hasPermission = await requestPermissions();
      if (!hasPermission) {
        Alert.alert(t('alerts.permissionRequired'), t('alerts.pleaseEnableNotifications'));
        return;
      }

      // Show immediate test notification
      const { scheduleNotificationAsync } = await import('expo-notifications');
      await scheduleNotificationAsync({
        content: {
          title: '🧪 Test Notification',
          body: 'This is a test of your medicine expiration notifications. Everything is working correctly!',
          data: { type: 'test' },
          sound: 'default',
        },
        trigger: null, // Show immediately
      });

      Alert.alert(t('alerts.testNotificationSent'), t('alerts.testNotificationMessage'));
    } catch (error) {
      console.error('Error sending test notification:', error);
      Alert.alert(t('common.error'), t('alerts.failedToSendTest'));
    }
  };

  if (loading || !config) {
    return (
      <Card title={t('common.loading')} style={{ padding: 16 }}>
        <Text className="text-center text-muted-foreground dark:text-neutral-400">{t('settings.loadingNotificationSettings')}</Text>
      </Card>
    );
  }

  return (
    <View className="space-y-4">
      <Card title={t('settings.medicineExpirationNotifications')} style={{ padding: 16 }}>
        {/* Main Enable/Disable Toggle */}
        <View className="flex-row justify-between items-center mb-4">
          <View className="flex-1 mr-4">
            <Text className="text-base font-medium text-foreground dark:text-neutral-100">{t('settings.enableNotifications')}</Text>
            <Text className="text-sm text-muted-foreground dark:text-neutral-400">
              {t('settings.getNotifiedWhenExpiring')}
            </Text>
          </View>
          <Switch
            value={config.enabled}
            onValueChange={handleEnableToggle}
            trackColor={{ false: '#d1d5db', true: '#3b82f6' }}
            thumbColor={config.enabled ? '#ffffff' : '#f3f4f6'}
          />
        </View>

        {config.enabled && (
          <>
            {/* Notification Time */}
            <View className="mb-4">
              <Text className="text-base font-medium text-foreground dark:text-neutral-100 mb-2">{t('settings.notificationTime')}</Text>
              <Button
                title={`${config.notificationTime} (24-hour format)`}
                variant="outline"
                onPress={showTimePicker}
                fullWidth
              />
              <Text className="text-xs text-muted-foreground dark:text-neutral-400 mt-1">
                {t('settings.timeWhenDailyNotificationsSent')}
              </Text>
            </View>

            {/* Notification Timing Options */}
            <View className="space-y-3 mb-4">
              <Text className="text-base font-medium text-foreground dark:text-neutral-100">{t('settings.whenToNotify')}</Text>
              
              <View className="flex-row justify-between items-center">
                <View className="flex-1 mr-4">
                  <Text className="text-sm font-medium text-foreground dark:text-neutral-100">{t('settings.threeDaysBefore')}</Text>
                  <Text className="text-xs text-muted-foreground dark:text-neutral-400">{t('settings.earlyWarningForRefills')}</Text>
                </View>
                <Switch
                  value={config.notifyThreeDaysBefore}
                  onValueChange={(value) => updateConfig({ notifyThreeDaysBefore: value })}
                  trackColor={{ false: '#d1d5db', true: '#3b82f6' }}
                  thumbColor={config.notifyThreeDaysBefore ? '#ffffff' : '#f3f4f6'}
                />
              </View>

              <View className="flex-row justify-between items-center">
                <View className="flex-1 mr-4">
                  <Text className="text-sm font-medium text-foreground dark:text-neutral-100">{t('settings.oneDayBefore')}</Text>
                  <Text className="text-xs text-muted-foreground dark:text-neutral-400">{t('settings.tomorrowExpirationReminder')}</Text>
                </View>
                <Switch
                  value={config.notifyOneDayBefore}
                  onValueChange={(value) => updateConfig({ notifyOneDayBefore: value })}
                  trackColor={{ false: '#d1d5db', true: '#3b82f6' }}
                  thumbColor={config.notifyOneDayBefore ? '#ffffff' : '#f3f4f6'}
                />
              </View>

              <View className="flex-row justify-between items-center">
                <View className="flex-1 mr-4">
                  <Text className="text-sm font-medium text-foreground dark:text-neutral-100">{t('settings.dayOfExpiration')}</Text>
                  <Text className="text-xs text-muted-foreground dark:text-neutral-400">{t('settings.expiresTodayAlert')}</Text>
                </View>
                <Switch
                  value={config.notifyToday}
                  onValueChange={(value) => updateConfig({ notifyToday: value })}
                  trackColor={{ false: '#d1d5db', true: '#3b82f6' }}
                  thumbColor={config.notifyToday ? '#ffffff' : '#f3f4f6'}
                />
              </View>
            </View>

            {/* Status Information */}
            <View className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg mb-4">
              <Text className="text-sm font-medium text-blue-800 dark:text-blue-200">
                {t('settings.status')}
              </Text>
              <Text className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                {pendingCount > 0 
                  ? t('settings.notificationsScheduled', { count: pendingCount })
                  : t('settings.noNotificationsScheduled')
                }
              </Text>
            </View>

            {/* Test Button */}
            <Button
              title={t('settings.sendTestNotification')}
              variant="outline"
              onPress={testNotifications}
              fullWidth
            />
          </>
        )}

        {!config.enabled && (
          <View className="bg-muted dark:bg-neutral-800 p-3 rounded-lg">
            <Text className="text-sm text-muted-foreground dark:text-neutral-400 text-center">
              {t('settings.enableNotificationsToReceiveAlerts')}
            </Text>
          </View>
        )}
      </Card>

      {/* iOS Time Picker Modal */}
      {Platform.OS === 'ios' && (
        <Modal
          visible={showIOSTimePicker}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowIOSTimePicker(false)}
        >
          <View className="flex-1 justify-end bg-black/50">
            <View className="bg-background dark:bg-neutral-800 p-4 rounded-t-2xl">
              <View className="flex-row justify-between items-center mb-4">
                <Text className="text-lg font-semibold text-foreground dark:text-neutral-100">{t('settings.selectTime')}</Text>
                <Button
                  title={t('common.done')}
                  onPress={() => setShowIOSTimePicker(false)}
                  variant="primary"
                />
              </View>
              {config && (
                <DateTimePicker
                  value={(() => {
                    const [hours, minutes] = config.notificationTime.split(':').map(Number);
                    const date = new Date();
                    date.setHours(hours, minutes, 0, 0);
                    return date;
                  })()}
                  mode="time"
                  display="spinner"
                  onChange={handleIOSTimeChange}
                  style={{ backgroundColor: isDark ? 'rgb(38 38 38)' : 'white' }}
                />
              )}
            </View>
          </View>
        </Modal>
      )}

      {/* Help Information */}
      <Card title={t('settings.howItWorks')} style={{ padding: 16 }}>
        <Text className="text-sm text-muted-foreground dark:text-neutral-400 leading-relaxed">
          {t('settings.howItWorksDescription')}
        </Text>
      </Card>
    </View>
  );
}
