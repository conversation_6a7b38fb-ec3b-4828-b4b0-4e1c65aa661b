import React from 'react';
import { View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@react-navigation/native';
import Card from '../ui/Card';
import { Patient } from '@/hooks/entities/usePatients'; // Assuming Patient type is exported from usePatients hook
import { useTranslation } from '@/hooks/useTranslation';

interface PatientCardProps {
  patient: Patient;
  onPress?: () => void;
}

/**
 * A card component for displaying patient information in a list.
 */
const PatientCard: React.FC<PatientCardProps> = ({ patient, onPress }) => {
  const { t } = useTranslation();
  const theme = useTheme();
  
  const content = (
    <View className="space-y-1">
      {/* Age */}
      {patient.age !== null && patient.age !== undefined && (
        <View className="flex-row items-center">
          <Ionicons name="calendar-outline" size={16} color={theme.colors.text} />
          <Text className="text-sm text-gray-600 ml-1">{t('misc.age')}: {patient.age}</Text>
        </View>
      )}
      {/* Blood Type */}
      {patient.bloodtype && (
        <View className="flex-row items-center">
          <Ionicons name="water-outline" size={16} color={theme.colors.notification} />
          <Text className="text-sm text-gray-600 ml-1">{t('misc.bloodType')}: {patient.bloodtype}</Text>
        </View>
      )}
    </View>
  );

  return (
    <Card
      title={patient.name}
      content={content}
      onPress={onPress}
      rightAction={
        <Ionicons name="chevron-forward" size={20} color={theme.colors.text} />
      }
    />
  );
};

export default PatientCard; 