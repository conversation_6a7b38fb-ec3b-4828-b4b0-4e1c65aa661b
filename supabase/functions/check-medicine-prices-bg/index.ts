import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { DOM<PERSON>arser } from "https://deno.land/x/deno_dom@v0.1.38/deno-dom-wasm.ts";

/**
 * MEDICINE PRICE CHECKER - SUPABASE EDGE FUNCTION
 * 
 * This function searches Bulgarian pharmacy websites for medicine prices
 * using DuckDuckGo search and OpenAI for price extraction.
 * 
 * WORKFLOW:
 * 1. Receives medicine name and user location from mobile app
 * 2. For each pharmacy site, constructs a DuckDuckGo search query
 * 3. Extracts relevant pharmacy page URLs from search results
 * 4. Fetches each pharmacy page and uses OpenAI to extract price info
 * 5. Returns structured price data to the mobile app
 */

// Structure for price results returned to the app
interface PriceResult {
  pharmacy_name: string;    // Name of the pharmacy (e.g., "apteka")
  price: number | null;     // Price in BGN or null if not found
  currency: string;         // Currency code (usually "BGN")
  url: string;             // Direct link to the medicine page
  availability: boolean;    // Whether the medicine is in stock
}

// Structure for incoming request from mobile app
interface RequestBody {
  medicineName: string;     // Name of the medicine to search for
  userLocation: string;     // User's location (city, region) for local search
}

// Search finds relevant Bulgarian pharmacies automatically

serve(async (req: Request) => {
  // Handle CORS preflight requests from the mobile app
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  }

  try {
    // Parse incoming request from mobile app
    const { medicineName, userLocation }: RequestBody = await req.json();

    // Validate required parameters
    if (!medicineName) {
      return new Response(
        JSON.stringify({ error: 'Medicine name is required' }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const results: PriceResult[] = [];

    // SINGLE SEARCH: Get top 10 results from any Bulgarian pharmacy
    try {
      // Step 1: Construct comprehensive search query for all pharmacies
      const searchQuery = `${medicineName} ${userLocation} цена`;
      const searchUrl = `https://duckduckgo.com/html/?q=${encodeURIComponent(searchQuery)}`;
      
      // Step 2: Fetch search results
      const response = await fetch(searchUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch search results');
      }

      // Step 3: Parse search results
      const html = await response.text();
      const doc = new DOMParser().parseFromString(html, "text/html");

      if (!doc) {
        throw new Error('Failed to parse search results');
      }

      // Step 4: Extract top 10 pharmacy page URLs
      const links = doc.querySelectorAll('.result__title a, .result__a');
      const topResults = Array.from(links as NodeListOf<Element>)
        .map((link) => ({
          title: link.textContent?.trim() || '',
          url: link.getAttribute('href') || ''
        }))
        .filter(link => link.url && link.url.includes('.bg')) // Bulgarian sites
        .slice(0, 10); // Top 10 results

      // Step 5: Process each result with AI
      for (const link of topResults) {
        try {
          // Fetch pharmacy page content with timeout
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
          
          const pageResponse = await fetch(link.url, {
            signal: controller.signal,
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
          });
          
          clearTimeout(timeoutId);

          if (!pageResponse.ok) continue;

          const pageHtml = await pageResponse.text();
          
          // Step 6: Extract price with AI
          const priceInfo = await extractPriceWithAI(pageHtml, medicineName);
          
          // Step 7: Validate and add results
          if (priceInfo && 
              priceInfo.price !== null && 
              typeof priceInfo.price === 'number' && 
              priceInfo.price > 0) {
            results.push({
              pharmacy_name: priceInfo.pharmacy_name,
              price: priceInfo.price,
              currency: priceInfo.currency || 'BGN',
              url: link.url,
              availability: priceInfo.availability !== false
            });
          }
        } catch (error) {
          console.error(`Error processing ${link.url}:`, error);
          // Continue with next result
        }
      }
    } catch (error) {
      console.error('Error in main search:', error);
      throw error;
    }

    // Return top 10 results with valid prices to the mobile app
    const validResults = results
      .filter(result => result.price !== null && result.price > 0)
      .sort((a, b) => (a.price || 0) - (b.price || 0)) // Sort by price ascending
      .slice(0, 10); // Return top 10 results

    return new Response(JSON.stringify(validResults), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });

  } catch (error) {
    console.error('Error in price checking function:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
});


/**
 * AI PRICE EXTRACTION FUNCTION
 * 
 * This function uses OpenAI to extract price information from Bulgarian pharmacy pages.
 * It handles the complex task of parsing unstructured HTML and Bulgarian text to find
 * medicine prices, availability, and currency information.
 * 
 * PROCESS:
 * 1. Clean HTML content by removing scripts, styles, and tags
 * 2. Create a focused prompt for OpenAI with Bulgarian keywords
 * 3. Send content to OpenAI for structured extraction
 * 4. Parse and return structured price data
 * 
 * WHY AI?: Bulgarian pharmacy websites have different layouts, languages, and formats.
 * AI can handle this variability better than rigid parsing rules.
 */
async function extractPriceWithAI(html: string, medicineName: string): Promise<{ price: number | null; currency: string; availability: boolean; pharmacy_name: string } | null> {
  try {
    // Get OpenAI API key from environment variables
    const openaiApiKey = Deno.env.get('OPENAI_API_KEY');
    if (!openaiApiKey) {
      throw new Error('OpenAI API key not found');
    }

    // STEP 1: Send raw HTML to AI - no preprocessing
    // Only limit size to stay within API token limits
    const rawHtml = html.slice(0, 15000); // Keep more content for better context

    // STEP 2: Create task-based AI prompt leveraging natural understanding
    const prompt = `Analyze this Bulgarian pharmacy webpage to extract pricing information for the medicine "${medicineName}".

HTML Content: ${rawHtml}

Task: Find the selling price that a customer would pay to purchase this specific medicine.

Key Instructions:
- Extract the actual purchase price (what customer pays at checkout)
- Ignore product specifications, technical details, dosage information  
- Use your understanding of Bulgarian language and e-commerce context
- Distinguish between selling price vs other numbers on the page
- Extract the pharmacy name from the website

Return this exact JSON structure:
{
  "price": [decimal_number_or_null],
  "currency": "BGN",
  "availability": [boolean],
  "pharmacy_name": "[extracted_pharmacy_name]"
}

Return null for price if no selling price is found for this medicine.`;

    // STEP 3: Send request to OpenAI for structured extraction
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4.1-nano',        // Fast, cost-effective model for extraction
        messages: [
          { role: 'user', content: prompt }
        ],
        max_tokens: 150,              // Limit for JSON response only
        temperature: 0.1              // Low temperature for consistent extraction
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    const content = data.choices[0]?.message?.content;
    
    // STEP 4: Parse and validate AI response
    try {
      const parsed = JSON.parse(content);
      
      // Validate response structure
      if (typeof parsed === 'object' && parsed !== null) {
        return {
          price: typeof parsed.price === 'number' ? parsed.price : null,
          currency: typeof parsed.currency === 'string' ? parsed.currency : 'BGN',
          availability: typeof parsed.availability === 'boolean' ? parsed.availability : false,
          pharmacy_name: typeof parsed.pharmacy_name === 'string' ? parsed.pharmacy_name : 'Unknown'
        };
      }
      
      return null;
    } catch (parseError) {
      console.error('Failed to parse OpenAI response:', content);
      return null; // Return null if parsing fails - don't crash the entire search
    }
  } catch (error) {
    console.error('Error in OpenAI extraction:', error);
    return null; // Return null if AI extraction fails - don't crash the entire search
  }
}