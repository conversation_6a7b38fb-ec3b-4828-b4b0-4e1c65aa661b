import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useSupabaseClient } from './useSupabaseClient';
import { useUserLocation } from './useUserLocation';
import { useRateLimitStore } from '@/store/rateLimitStore';

/**
 * MEDICINE PRICE CHECKING HOOK
 * 
 * This hook manages the medicine price checking feature using React Query
 * and Supabase Edge Functions. It handles location detection, API calls,
 * caching, and error states for a smooth user experience.
 * 
 * FEATURES:
 * - Integrates with location services for local search
 * - Uses React Query for caching and optimistic updates
 * - Handles loading states and error recovery
 * - Caches results to reduce API calls and improve performance
 * - Provides structured data for UI components
 * 
 * WORKFLOW:
 * 1. User taps "Check Prices" button on medicine card
 * 2. Hook gets user's location (cached or fresh)
 * 3. Calls Supabase Edge Function with medicine name and location
 * 4. Edge Function searches Bulgarian pharmacy sites
 * 5. Results are cached and returned to UI
 */

// Structure for individual price results from pharmacies
export interface PriceResult {
  pharmacy_name: string;  // Name of the pharmacy
  price: number | null;   // Price in local currency or null if not found
  currency: string;       // Currency code (usually "BGN" for Bulgaria)
  url: string;           // Direct link to the medicine page
  availability: boolean; // Whether the medicine is in stock
}

// Structure for the complete response from the Edge Function
interface PriceCheckResponse {
  results: PriceResult[];   // Array of price results from different pharmacies
  medicineName: string;     // Name of the searched medicine
  searchLocation: string;   // Location used for the search
  timestamp: string;        // When the search was performed
}

export function useCheckMedicinePrices() {
  const queryClient = useQueryClient();
  const { getLocationForSearch, getCurrentLocation } = useUserLocation();
  const { supabase } = useSupabaseClient();
  const { checkRateLimit, recordRequest, getRemainingRequests, getTimeUntilNextRequest, setPriceCheckingMedicine } = useRateLimitStore();

  // Create a React Query mutation for price checking
  const mutation = useMutation<PriceCheckResponse, Error, { medicineName: string; medicineId: string }>({
    mutationFn: async ({ medicineName, medicineId }) => {
      // PHASE 1: Permission check (no screen loading yet)
      
      // STEP 1: Check rate limit first
      const rateLimitResult = checkRateLimit();
      if (!rateLimitResult.allowed) {
        throw new Error(`RATE_LIMIT_EXCEEDED:${rateLimitResult.remainingTime}`);
      }

      // STEP 2: Validate input
      if (!medicineName.trim()) {
        throw new Error('Medicine name is required');
      }

      if (!supabase) {
        throw new Error('Supabase client not available');
      }

      // STEP 3: Get user location for targeted search - REQUIRED
      const { location: currentLocation, error: locationError } = await getCurrentLocation();
      const searchLocation = getLocationForSearch(currentLocation);
      
      // Location is required for Bulgarian pharmacy search
      if (!searchLocation) {
        // Pass through the specific location error
        if (locationError) {
          throw new Error(locationError);
        } else {
          throw new Error('LOCATION_REQUIRED');
        }
      }

      // PHASE 2: API call (now set screen loading)
      setPriceCheckingMedicine(medicineId);

      // STEP 4: Record the request for rate limiting
      recordRequest();

      // STEP 5: Call Supabase Edge Function to search pharmacies
      const { data, error } = await supabase.functions.invoke('check-medicine-prices-bg', {
        body: {
          medicineName: medicineName.trim(),
          userLocation: searchLocation
        }
      });

      if (error) {
        throw new Error(`Failed to check prices: ${error.message}`);
      }

      if (!data) {
        throw new Error('No data received from price checking service');
      }

      // STEP 6: Filter and validate results
      // Only include results with valid data (price OR availability info)
      const validResults = (data as PriceResult[]).filter(result => 
        result && 
        result.pharmacy_name && 
        result.url &&
        (result.price !== null || result.availability === false)
      );

      // STEP 7: Return structured response
      return {
        results: validResults,
        medicineName,
        searchLocation,
        timestamp: new Date().toISOString()
      };
    },
    onSuccess: () => {
      // Clear the medicine ID when successful
      setPriceCheckingMedicine(null);
    },
    onError: (error) => {
      console.error('Price checking failed:', error);
      // Clear the medicine ID when failed
      setPriceCheckingMedicine(null);
    }
  });

  return {
    checkPrices: mutation.mutateAsync,  // Function to trigger price check
    isPending: mutation.isPending,      // Loading state for UI
    error: mutation.error,              // Error object if request fails
    isError: mutation.isError,          // Boolean error state
    data: mutation.data,                // Last successful result
    reset: mutation.reset,              // Function to reset mutation state
    // Rate limiting info
    remainingRequests: getRemainingRequests(),
    timeUntilNextRequest: getTimeUntilNextRequest(),
    isRateLimited: !checkRateLimit().allowed
  };
}

