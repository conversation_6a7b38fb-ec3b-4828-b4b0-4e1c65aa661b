import { useState } from 'react';
import * as Location from 'expo-location';
import { Platform } from 'react-native';

/**
 * USER LOCATION HOOK
 * 
 * This hook manages user location for medicine price checking in Bulgaria.
 * It follows Expo best practices for location permissions and respects user privacy choices.
 * 
 * FEATURES:
 * - Requests location permissions properly using Expo hooks
 * - Uses Expo's built-in location caching via getLastKnownPositionAsync()
 * - Respects "Allow Once" permission by not maintaining app-level cache
 * - Handles permission denials gracefully
 * - Provides location in search-friendly format (city, region)
 * - Battery-efficient with system-level caching
 * 
 * WORKFLOW:
 * 1. Check permission status before accessing location
 * 2. If no permission, request permission (respecting "Allow Once")
 * 3. Use getLastKnownPositionAsync() for fast cached location
 * 4. Fall back to getCurrentPositionAsync() if no cached location
 * 5. Reverse geocode to get city/region names
 * 6. Return formatted location string for search queries
 */

interface LocationData {
  city: string;          // City name (e.g., "Sofia")
  region: string;        // Region name (e.g., "Sofia Province")
  country: string;       // Country name (should be "Bulgaria")
  coordinates: {
    latitude: number;    // GPS latitude
    longitude: number;   // GPS longitude
  };
}

export function useUserLocation() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [permission, setPermission] = useState<Location.LocationPermissionResponse | null>(null);


  /**
   * Get current user location using direct Expo location API calls
   * 
   * PROCESS:
   * 1. Check environment (web vs native)
   * 2. Check system location services
   * 3. Request location permissions directly
   * 4. Get location data
   * 5. Reverse geocode coordinates
   */
  const getCurrentLocation = async (): Promise<{ location: LocationData | null; error: string | null }> => {
    setIsLoading(true);
    setError(null);

    try {
      // STEP 1: Environment detection
      console.log('Platform:', Platform.OS);
      
      if (Platform.OS === 'web') {
        console.log('Running on web - location permissions work differently');
        const errorType = 'LOCATION_WEB_NOT_SUPPORTED';
        setError(errorType);
        setIsLoading(false);
        return { location: null, error: errorType };
      }

      // STEP 2: Check if location services are enabled at OS level
      const servicesEnabled = await Location.hasServicesEnabledAsync();
      console.log('Location services enabled:', servicesEnabled);
      
      if (!servicesEnabled) {
        console.log('Location services disabled at OS level');
        const errorType = 'LOCATION_SERVICES_DISABLED';
        setError(errorType);
        setIsLoading(false);
        return { location: null, error: errorType };
      }

      // STEP 3: Get current permission status
      const currentPermission = await Location.getForegroundPermissionsAsync();
      console.log('Current permission status:', currentPermission);
      setPermission(currentPermission);
      
      // STEP 4: Request permission if needed
      if (currentPermission.granted !== true) {
        // Check if we can ask again before requesting
        if (currentPermission.canAskAgain === false) {
          const errorType = 'LOCATION_PERMISSION_PERMANENTLY_DENIED';
          setError(errorType);
          setIsLoading(false);
          return { location: null, error: errorType };
        }
        
        console.log('Requesting location permission...');
        const permissionResult = await Location.requestForegroundPermissionsAsync();
        console.log('Permission request result:', permissionResult);
        setPermission(permissionResult);
        
        if (permissionResult.granted !== true) {
          console.log('Permission denied:', permissionResult);
          
          const errorType = permissionResult.canAskAgain === false 
            ? 'LOCATION_PERMISSION_PERMANENTLY_DENIED' 
            : 'LOCATION_PERMISSION_DENIED';
          
          setError(errorType);
          setIsLoading(false);
          return { location: null, error: errorType };
        }
      }

      // STEP 5: Get location data
      console.log('Getting location data...');
      let currentLocation = await Location.getLastKnownPositionAsync();
      
      if (!currentLocation) {
        console.log('No cached location, getting fresh location...');
        currentLocation = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Balanced,
          timeInterval: 10000,
        });
      }

      console.log('Location obtained:', currentLocation);

      // STEP 6: Reverse geocode
      const geocoded = await Location.reverseGeocodeAsync({
        latitude: currentLocation.coords.latitude,
        longitude: currentLocation.coords.longitude
      });

      if (geocoded.length > 0) {
        const address = geocoded[0];
        console.log('Geocoded address:', address);
        
        const locationData: LocationData = {
          city: address.city || address.district || 'Unknown',
          region: address.region || address.subregion || 'Unknown',
          country: address.country || 'Bulgaria',
          coordinates: {
            latitude: currentLocation.coords.latitude,
            longitude: currentLocation.coords.longitude
          }
        };

        setIsLoading(false);
        return { location: locationData, error: null };
      } else {
        const errorType = 'LOCATION_GEOCODING_FAILED';
        setError(errorType);
        setIsLoading(false);
        return { location: null, error: errorType };
      }
    } catch (error) {
      console.error('Error getting location:', error);
      const errorType = error instanceof Error ? error.message : 'LOCATION_UNKNOWN_ERROR';
      setError(errorType);
      setIsLoading(false);
      return { location: null, error: errorType };
    }
  };

  /**
   * Format location data for search queries
   * 
   * Returns the most specific location available in a search-friendly format.
   * Priority: "City, Region" > "City" > "Region" > null
   * Returns null if no location data is available - caller must handle this
   */
  const getLocationForSearch = (locationData: LocationData | null): string | null => {
    if (!locationData) return null;
    
    // Return city, region format for better search results
    if (locationData.city && locationData.region) {
      return `${locationData.city}, ${locationData.region}`;
    }
    
    return locationData.city || locationData.region || null;
  };

  return {
    isLoading,                   // Loading state for UI
    error,                       // Error message if location fails
    permission,                  // Current permission status
    getCurrentLocation,          // Function to get location (cached or fresh)
    getLocationForSearch,        // Function to get formatted location string
    hasPermission: permission?.granted === true,
    platformSupported: Platform.OS !== 'web'  // Whether platform supports location
  };
}