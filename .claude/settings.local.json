{"permissions": {"allow": ["Bash(bun run:*)", "<PERSON><PERSON>(true)", "Bash(rg:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(find:*)", "Bash(grep:*)", "Bash(ls:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "<PERSON><PERSON>(mv:*)", "WebFetch(domain:docs.expo.dev)", "mcp__ide__getDiagnostics", "mcp__supabase__generate_typescript_types", "mcp__supabase__list_projects"], "deny": []}}